<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>812所质量信息看板</title>
    <style>
        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* 顶部标题区域 */
        .header {
            background: linear-gradient(90deg, #1976D2 0%, #1565C0 100%);
            color: white;
            text-align: center;
            padding: 20px 0;
            box-shadow: 0 4px 8px rgba(25, 118, 210, 0.3);
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 600;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
        }

        /* 主体容器 */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px 20px;
            height: calc(100vh - 120px);
        }

        /* 2x2网格布局 */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 30px;
            height: 100%;
        }

        /* 模块卡片基础样式 */
        .module-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(25, 118, 210, 0.15);
            border: 1px solid rgba(25, 118, 210, 0.1);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateY(30px);
            animation: slideInUp 0.8s ease forwards;
        }

        .module-card:nth-child(1) { animation-delay: 0.1s; }
        .module-card:nth-child(2) { animation-delay: 0.2s; }
        .module-card:nth-child(3) { animation-delay: 0.3s; }
        .module-card:nth-child(4) { animation-delay: 0.4s; }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(25, 118, 210, 0.25);
        }

        .module-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #1976D2, #42A5F5);
        }

        /* 模块标题 */
        .module-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #1976D2;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .module-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 2px;
            background: linear-gradient(90deg, #1976D2, #42A5F5);
        }

        /* 数据卡片容器 */
        .data-cards {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            height: calc(100% - 80px);
        }

        /* 单个数据卡片 */
        .data-card {
            background: linear-gradient(135deg, #F3F9FF 0%, #E8F4FD 100%);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(25, 118, 210, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .data-card:hover {
            background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);
            transform: scale(1.02);
        }

        .data-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(25, 118, 210, 0.1) 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .data-card:hover::before {
            opacity: 1;
        }

        .data-number {
            font-size: 2.2rem;
            font-weight: 700;
            color: #1976D2;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .data-label {
            font-size: 0.9rem;
            color: #666;
            font-weight: 500;
            position: relative;
            z-index: 1;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .container {
                padding: 20px 15px;
            }
            
            .dashboard-grid {
                gap: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
                grid-template-rows: repeat(4, 1fr);
                gap: 15px;
            }
            
            .container {
                height: auto;
                padding: 15px 10px;
            }
            
            .header h1 {
                font-size: 1.6rem;
            }
            
            .module-card {
                padding: 20px;
            }
            
            .data-cards {
                grid-template-columns: 1fr 1fr;
                gap: 10px;
            }
        }

        @media (max-width: 480px) {
            .data-cards {
                grid-template-columns: 1fr;
            }
            
            .data-number {
                font-size: 1.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部标题区域 -->
    <header class="header">
        <h1>812所质量信息看板</h1>
    </header>

    <!-- 主体容器 -->
    <div class="container">
        <div class="dashboard-grid">
            <!-- 现场问题监控模块 (左上) -->
            <div class="module-card">
                <h2 class="module-title">现场问题监控</h2>
                <div class="data-cards">
                    <div class="data-card">
                        <div class="data-number" data-target="23">0</div>
                        <div class="data-label">集团级问题</div>
                    </div>
                    <div class="data-card">
                        <div class="data-number" data-target="15">0</div>
                        <div class="data-label">院级问题</div>
                    </div>
                    <div class="data-card">
                        <div class="data-number" data-target="8">0</div>
                        <div class="data-label">所级问题</div>
                    </div>
                    <div class="data-card">
                        <div class="data-number" data-target="5">0</div>
                        <div class="data-label">其他问题</div>
                    </div>
                </div>
            </div>

            <!-- 测试异常追踪模块 (右上) -->
            <div class="module-card">
                <h2 class="module-title">测试异常追踪</h2>
                <div class="data-cards">
                    <div class="data-card">
                        <div class="data-number" data-target="12">0</div>
                        <div class="data-label">集团级异常</div>
                    </div>
                    <div class="data-card">
                        <div class="data-number" data-target="18">0</div>
                        <div class="data-label">院级异常</div>
                    </div>
                    <div class="data-card">
                        <div class="data-number" data-target="7">0</div>
                        <div class="data-label">所级异常</div>
                    </div>
                    <div class="data-card">
                        <div class="data-number" data-target="3">0</div>
                        <div class="data-label">其他异常</div>
                    </div>
                </div>
            </div>

            <!-- 不合格品审理分析模块 (左下) -->
            <div class="module-card">
                <h2 class="module-title">不合格品审理分析</h2>
                <div class="data-cards">
                    <div class="data-card">
                        <div class="data-number" data-target="31">0</div>
                        <div class="data-label">一级审理</div>
                    </div>
                    <div class="data-card">
                        <div class="data-number" data-target="24">0</div>
                        <div class="data-label">二级审理</div>
                    </div>
                    <div class="data-card">
                        <div class="data-number" data-target="16">0</div>
                        <div class="data-label">三级审理</div>
                    </div>
                    <div class="data-card">
                        <div class="data-number" data-target="9">0</div>
                        <div class="data-label">其他审理</div>
                    </div>
                </div>
            </div>

            <!-- 现场临时处理监控模块 (右下) -->
            <div class="module-card">
                <h2 class="module-title">现场临时处理监控</h2>
                <div class="data-cards">
                    <div class="data-card">
                        <div class="data-number" data-target="6">0</div>
                        <div class="data-label">报警遥测波道屏蔽</div>
                    </div>
                    <div class="data-card">
                        <div class="data-number" data-target="11">0</div>
                        <div class="data-label">遥测遥控指令变更</div>
                    </div>
                    <div class="data-card">
                        <div class="data-number" data-target="4">0</div>
                        <div class="data-label">加载软件修改</div>
                    </div>
                    <div class="data-card">
                        <div class="data-number" data-target="8">0</div>
                        <div class="data-label">变更程序增减项目</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 数字动画函数 (ES5语法)
        function animateNumber(element, target, duration) {
            var start = 0;
            var startTime = null;
            
            function animation(currentTime) {
                if (startTime === null) startTime = currentTime;
                var progress = Math.min((currentTime - startTime) / duration, 1);
                var current = Math.floor(progress * target);
                element.textContent = current;
                
                if (progress < 1) {
                    requestAnimationFrame(animation);
                }
            }
            
            requestAnimationFrame(animation);
        }

        // 页面加载完成后启动动画
        window.addEventListener('load', function() {
            setTimeout(function() {
                var numberElements = document.querySelectorAll('.data-number');
                
                for (var i = 0; i < numberElements.length; i++) {
                    var element = numberElements[i];
                    var target = parseInt(element.getAttribute('data-target'));
                    var delay = i * 100; // 错开动画时间
                    
                    (function(el, tgt, dly) {
                        setTimeout(function() {
                            animateNumber(el, tgt, 2000);
                        }, dly);
                    })(element, target, delay);
                }
            }, 1000);
        });

        // 添加卡片悬停效果增强
        var dataCards = document.querySelectorAll('.data-card');
        for (var i = 0; i < dataCards.length; i++) {
            dataCards[i].addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05) translateY(-2px)';
            });
            
            dataCards[i].addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1) translateY(0)';
            });
        }
    </script>
</body>
</html>