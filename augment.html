<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级天气展示卡片</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0a0a0a;
            color: #ffffff;
            overflow: hidden;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* 全屏背景容器 */
        .weather-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            transition: all 0.8s ease-in-out;
        }

        /* 晴天背景 */
        .sunny-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        /* 雨天背景 */
        .rainy-bg {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }

        /* 下雪背景 */
        .snowy-bg {
            background: linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%);
        }

        /* 大风背景 */
        .windy-bg {
            background: linear-gradient(135deg, #8360c3 0%, #2ebf91 100%);
        }

        /* 动画粒子容器 */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        /* 太阳 */
        .sun {
            position: absolute;
            top: 15%;
            right: 15%;
            width: 120px;
            height: 120px;
            background: radial-gradient(circle, #ffd700 0%, #ffed4e 70%);
            border-radius: 50%;
            box-shadow: 0 0 60px rgba(255, 215, 0, 0.8);
            animation: sunRotate 20s linear infinite;
        }

        .sun::before {
            content: '';
            position: absolute;
            top: -20px;
            left: -20px;
            right: -20px;
            bottom: -20px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, transparent 70%);
        }

        @keyframes sunRotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 云朵 */
        .cloud {
            position: absolute;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 50px;
            opacity: 0.7;
        }

        .cloud::before,
        .cloud::after {
            content: '';
            position: absolute;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 50px;
        }

        .cloud1 {
            width: 80px;
            height: 40px;
            top: 20%;
            left: 10%;
            animation: cloudFloat1 15s ease-in-out infinite;
        }

        .cloud1::before {
            width: 50px;
            height: 50px;
            top: -25px;
            left: 10px;
        }

        .cloud1::after {
            width: 60px;
            height: 40px;
            top: -15px;
            right: 10px;
        }

        .cloud2 {
            width: 60px;
            height: 30px;
            top: 30%;
            right: 20%;
            animation: cloudFloat2 18s ease-in-out infinite;
        }

        .cloud2::before {
            width: 40px;
            height: 40px;
            top: -20px;
            left: 8px;
        }

        .cloud2::after {
            width: 50px;
            height: 30px;
            top: -10px;
            right: 8px;
        }

        @keyframes cloudFloat1 {
            0%, 100% { transform: translateX(0px) translateY(0px); }
            25% { transform: translateX(20px) translateY(-10px); }
            50% { transform: translateX(0px) translateY(-20px); }
            75% { transform: translateX(-20px) translateY(-10px); }
        }

        @keyframes cloudFloat2 {
            0%, 100% { transform: translateX(0px) translateY(0px); }
            33% { transform: translateX(-15px) translateY(15px); }
            66% { transform: translateX(15px) translateY(-15px); }
        }

        /* 雨滴 */
        .raindrop {
            position: absolute;
            width: 2px;
            height: 20px;
            background: linear-gradient(to bottom, transparent, #4a90e2);
            border-radius: 0 0 2px 2px;
            animation: rainFall linear infinite;
        }

        @keyframes rainFall {
            to {
                transform: translateY(100vh);
            }
        }

        /* 雪花 */
        .snowflake {
            position: absolute;
            color: #fff;
            font-size: 1em;
            animation: snowFall linear infinite;
        }

        @keyframes snowFall {
            to {
                transform: translateY(100vh) rotate(360deg);
            }
        }

        /* 风粒子 */
        .wind-particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: windBlow linear infinite;
        }

        @keyframes windBlow {
            0% {
                transform: translateX(-100px) translateY(0px);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateX(calc(100vw + 100px)) translateY(-50px);
                opacity: 0;
            }
        }

        /* 主卡片容器 */
        .weather-card {
            position: relative;
            width: 400px;
            height: 500px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            padding: 40px 30px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            transition: all 0.5s ease;
            z-index: 10;
        }

        .weather-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
        }

        /* 卡片头部 */
        .card-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .city-name {
            font-size: 1.8rem;
            font-weight: 300;
            margin-bottom: 10px;
            opacity: 0.9;
        }

        .weather-icon {
            font-size: 4rem;
            margin: 20px 0;
            transition: all 0.3s ease;
        }

        .temperature {
            font-size: 3.5rem;
            font-weight: 100;
            margin: 20px 0;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .weather-description {
            font-size: 1.2rem;
            opacity: 0.8;
            font-weight: 300;
        }

        /* 天气详情 */
        .weather-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }

        .detail-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .detail-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .detail-label {
            font-size: 0.9rem;
            opacity: 0.7;
            margin-bottom: 5px;
        }

        .detail-value {
            font-size: 1.1rem;
            font-weight: 500;
        }

        /* 控制按钮组 */
        .weather-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }

        .weather-btn {
            width: 60px;
            height: 60px;
            border: none;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .weather-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-3px) scale(1.1);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }

        .weather-btn:active {
            transform: translateY(-1px) scale(1.05);
        }

        .weather-btn.active {
            background: rgba(255, 255, 255, 0.4);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }

        .weather-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transition: all 0.3s ease;
            transform: translate(-50%, -50%);
        }

        .weather-btn:hover::before {
            width: 100%;
            height: 100%;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .weather-card {
                width: 90vw;
                max-width: 350px;
                height: auto;
                min-height: 450px;
                padding: 30px 20px;
            }

            .temperature {
                font-size: 3rem;
            }

            .city-name {
                font-size: 1.5rem;
            }

            .weather-icon {
                font-size: 3.5rem;
            }

            .weather-controls {
                gap: 10px;
            }

            .weather-btn {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
            }
        }

        @media (max-width: 480px) {
            .weather-card {
                padding: 25px 15px;
            }

            .weather-details {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }

        /* 淡入淡出动画 */
        .fade-in {
            animation: fadeIn 0.8s ease-in-out;
        }

        .fade-out {
            animation: fadeOut 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeOut {
            from {
                opacity: 1;
                transform: translateY(0);
            }
            to {
                opacity: 0;
                transform: translateY(-20px);
            }
        }

        /* 隐藏元素 */
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <!-- 全屏背景 -->
    <div class="weather-background sunny-bg" id="weatherBackground">
        <!-- 动画粒子容器 -->
        <div class="particles" id="particles">
            <!-- 晴天元素 -->
            <div class="sun hidden" id="sun"></div>
            <div class="cloud cloud1 hidden" id="cloud1"></div>
            <div class="cloud cloud2 hidden" id="cloud2"></div>
        </div>
    </div>

    <!-- 主天气卡片 -->
    <div class="weather-card fade-in">
        <div class="card-header">
            <div class="city-name" id="cityName">北京</div>
            <div class="weather-icon" id="weatherIcon">☀️</div>
            <div class="temperature" id="temperature">25°C</div>
            <div class="weather-description" id="weatherDescription">晴朗</div>
        </div>

        <div class="weather-details">
            <div class="detail-item">
                <div class="detail-label">湿度</div>
                <div class="detail-value" id="humidity">65%</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">风速</div>
                <div class="detail-value" id="windSpeed">12 km/h</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">气压</div>
                <div class="detail-value" id="pressure">1013 hPa</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">能见度</div>
                <div class="detail-value" id="visibility">10 km</div>
            </div>
        </div>

        <!-- 天气控制按钮 -->
        <div class="weather-controls">
            <button class="weather-btn active" data-weather="sunny" title="晴天">
                ☀️
            </button>
            <button class="weather-btn" data-weather="rainy" title="雨天">
                🌧️
            </button>
            <button class="weather-btn" data-weather="snowy" title="下雪">
                ❄️
            </button>
            <button class="weather-btn" data-weather="windy" title="大风">
                💨
            </button>
        </div>
    </div>

    <script>
        class WeatherApp {
            constructor() {
                this.currentWeather = 'sunny';
                this.weatherData = {
                    sunny: {
                        icon: '☀️',
                        temperature: '25°C',
                        description: '晴朗',
                        humidity: '45%',
                        windSpeed: '8 km/h',
                        pressure: '1020 hPa',
                        visibility: '15 km',
                        bgClass: 'sunny-bg'
                    },
                    rainy: {
                        icon: '🌧️',
                        temperature: '18°C',
                        description: '小雨',
                        humidity: '85%',
                        windSpeed: '15 km/h',
                        pressure: '1005 hPa',
                        visibility: '8 km',
                        bgClass: 'rainy-bg'
                    },
                    snowy: {
                        icon: '❄️',
                        temperature: '-2°C',
                        description: '小雪',
                        humidity: '75%',
                        windSpeed: '10 km/h',
                        pressure: '1015 hPa',
                        visibility: '5 km',
                        bgClass: 'snowy-bg'
                    },
                    windy: {
                        icon: '💨',
                        temperature: '22°C',
                        description: '大风',
                        humidity: '55%',
                        windSpeed: '35 km/h',
                        pressure: '1010 hPa',
                        visibility: '12 km',
                        bgClass: 'windy-bg'
                    }
                };

                this.init();
            }

            init() {
                this.bindEvents();
                this.showSunnyWeather();
            }

            bindEvents() {
                const weatherBtns = document.querySelectorAll('.weather-btn');
                weatherBtns.forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const weather = e.target.getAttribute('data-weather');
                        this.changeWeather(weather);
                    });
                });
            }

            changeWeather(weather) {
                if (weather === this.currentWeather) return;

                // 更新按钮状态
                document.querySelectorAll('.weather-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.querySelector(`[data-weather="${weather}"]`).classList.add('active');

                // 清除当前动画
                this.clearAnimations();

                // 更新背景
                this.updateBackground(weather);

                // 更新卡片信息
                this.updateWeatherInfo(weather);

                // 显示新的动画效果
                setTimeout(() => {
                    this.showWeatherAnimation(weather);
                }, 300);

                this.currentWeather = weather;
            }

            updateBackground(weather) {
                const background = document.getElementById('weatherBackground');
                const data = this.weatherData[weather];

                // 移除所有背景类
                background.className = 'weather-background';

                // 添加新的背景类
                setTimeout(() => {
                    background.classList.add(data.bgClass);
                }, 50);
            }

            updateWeatherInfo(weather) {
                const data = this.weatherData[weather];

                document.getElementById('weatherIcon').textContent = data.icon;
                document.getElementById('temperature').textContent = data.temperature;
                document.getElementById('weatherDescription').textContent = data.description;
                document.getElementById('humidity').textContent = data.humidity;
                document.getElementById('windSpeed').textContent = data.windSpeed;
                document.getElementById('pressure').textContent = data.pressure;
                document.getElementById('visibility').textContent = data.visibility;
            }

            clearAnimations() {
                const particles = document.getElementById('particles');

                // 隐藏所有天气元素
                document.getElementById('sun').classList.add('hidden');
                document.getElementById('cloud1').classList.add('hidden');
                document.getElementById('cloud2').classList.add('hidden');

                // 清除雨滴
                const raindrops = particles.querySelectorAll('.raindrop');
                raindrops.forEach(drop => drop.remove());

                // 清除雪花
                const snowflakes = particles.querySelectorAll('.snowflake');
                snowflakes.forEach(flake => flake.remove());

                // 清除风粒子
                const windParticles = particles.querySelectorAll('.wind-particle');
                windParticles.forEach(particle => particle.remove());
            }

            showWeatherAnimation(weather) {
                switch(weather) {
                    case 'sunny':
                        this.showSunnyWeather();
                        break;
                    case 'rainy':
                        this.showRainyWeather();
                        break;
                    case 'snowy':
                        this.showSnowyWeather();
                        break;
                    case 'windy':
                        this.showWindyWeather();
                        break;
                }
            }

            showSunnyWeather() {
                document.getElementById('sun').classList.remove('hidden');
                document.getElementById('cloud1').classList.remove('hidden');
                document.getElementById('cloud2').classList.remove('hidden');
            }

            showRainyWeather() {
                const particles = document.getElementById('particles');

                // 创建雨滴
                for (let i = 0; i < 100; i++) {
                    setTimeout(() => {
                        this.createRaindrop(particles);
                    }, i * 50);
                }

                // 持续创建雨滴
                this.rainInterval = setInterval(() => {
                    if (this.currentWeather === 'rainy') {
                        this.createRaindrop(particles);
                    }
                }, 100);
            }

            createRaindrop(container) {
                const raindrop = document.createElement('div');
                raindrop.className = 'raindrop';
                raindrop.style.left = Math.random() * 100 + '%';
                raindrop.style.animationDuration = (Math.random() * 0.5 + 0.5) + 's';
                raindrop.style.animationDelay = Math.random() * 0.5 + 's';

                container.appendChild(raindrop);

                // 移除雨滴
                setTimeout(() => {
                    if (raindrop.parentNode) {
                        raindrop.remove();
                    }
                }, 2000);
            }

            showSnowyWeather() {
                const particles = document.getElementById('particles');
                const snowflakeSymbols = ['❄', '❅', '❆', '✻', '✼', '❈'];

                // 创建雪花
                for (let i = 0; i < 50; i++) {
                    setTimeout(() => {
                        this.createSnowflake(particles, snowflakeSymbols);
                    }, i * 100);
                }

                // 持续创建雪花
                this.snowInterval = setInterval(() => {
                    if (this.currentWeather === 'snowy') {
                        this.createSnowflake(particles, snowflakeSymbols);
                    }
                }, 300);
            }

            createSnowflake(container, symbols) {
                const snowflake = document.createElement('div');
                snowflake.className = 'snowflake';
                snowflake.textContent = symbols[Math.floor(Math.random() * symbols.length)];
                snowflake.style.left = Math.random() * 100 + '%';
                snowflake.style.animationDuration = (Math.random() * 3 + 2) + 's';
                snowflake.style.animationDelay = Math.random() * 2 + 's';
                snowflake.style.fontSize = (Math.random() * 0.8 + 0.8) + 'em';

                container.appendChild(snowflake);

                // 移除雪花
                setTimeout(() => {
                    if (snowflake.parentNode) {
                        snowflake.remove();
                    }
                }, 5000);
            }

            showWindyWeather() {
                const particles = document.getElementById('particles');

                // 创建风粒子
                for (let i = 0; i < 30; i++) {
                    setTimeout(() => {
                        this.createWindParticle(particles);
                    }, i * 200);
                }

                // 持续创建风粒子
                this.windInterval = setInterval(() => {
                    if (this.currentWeather === 'windy') {
                        this.createWindParticle(particles);
                    }
                }, 500);
            }

            createWindParticle(container) {
                const particle = document.createElement('div');
                particle.className = 'wind-particle';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDuration = (Math.random() * 2 + 1) + 's';
                particle.style.animationDelay = Math.random() * 1 + 's';

                container.appendChild(particle);

                // 移除粒子
                setTimeout(() => {
                    if (particle.parentNode) {
                        particle.remove();
                    }
                }, 3000);
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new WeatherApp();
        });
    </script>
</body>
</html>