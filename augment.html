<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>高级天气展示卡片 · 新拟态</title>
  <style>
    /* 确认：新拟态风格 —— Confirmed via 寸止 */
    :root{
      --bg:#0f1115; --fg:#e6e9ef; --muted:#a0a6b3; --card:#131720;
      --accent:#ffd057; --accent-2:#fff0b3; /* 默认晴天的强调色 */
    }
    /* 不同天气模式下的主题变量 */
    body[data-mode="sunny"]{ --bg:#0d1014; --accent:#ffd057; --accent-2:#fff0b3; }
    body[data-mode="rain"] { --bg:#0a0d12; --accent:#4da3ff; --accent-2:#8ec5ff; }
    body[data-mode="snow"] { --bg:#0a0e14; --accent:#9ed0ff; --accent-2:#e0f2ff; }
    body[data-mode="wind"] { --bg:#0a0f0d; --accent:#9fe6a0; --accent-2:#d2ffd4; }

    *{box-sizing:border-box}
    html,body{height:100%}
    body{
      margin:0; font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
      color:var(--fg); background:var(--bg);
      transition: background-color .4s ease, color .4s ease;
      overflow:hidden;
    }
    .app{position:relative; min-height:100%;}

    /* 全屏场景层 */
    .scene{position:fixed; inset:0; z-index:0;}
    .layer{position:absolute; inset:0; opacity:0; transition:opacity .4s ease; will-change:opacity; pointer-events:none}
    .layer.active{opacity:1}
    .layer .particle{position:absolute; will-change:transform,opacity;}
    .layer:not(.active) .particle{animation-play-state:paused}

    /* 晴天：天空与太阳光晕 + 浮动云 */
    #sunny-layer{background: radial-gradient(800px 800px at 75% 15%, rgba(255,208,87,.35), rgba(255,208,87,0) 60%), linear-gradient(180deg, #0e1219 0%, #111622 100%);}
    .sun{position:absolute; top:6vh; right:12vw; width:160px; height:160px; border-radius:50%; background: radial-gradient(circle at 50% 50%, #ffd057 0%, #ffedb0 40%, rgba(255,208,87,0) 70%); filter: blur(6px); opacity:.9}
    .cloud{position:absolute; background:rgba(255,255,255,.15); filter:blur(0.5px); border-radius:999px; box-shadow: 40px 10px 60px rgba(0,0,0,.15) inset; animation: cloudFloat linear infinite;}
    @keyframes cloudFloat{ from{ transform: translateX(-10vw)} to{ transform: translateX(110vw)} }

    /* 雨天：深蓝渐变 + 雨滴下落 */
    #rain-layer{background: linear-gradient(180deg, #0b111b 0%, #0a0f19 100%);}
    .drop{width:2px; height:70px; background: linear-gradient(180deg, rgba(255,255,255,.0), rgba(142,197,255,.9)); border-radius:1px; opacity:.7; animation: rainFall linear infinite;}
    @keyframes rainFall{ to{ transform: translateY(110vh)} }

    /* 下雪：偏冷色背景 + 软阴影雪花 */
    #snow-layer{background: radial-gradient(1000px 800px at 30% -10%, rgba(158,208,255,.25), rgba(158,208,255,0) 60%), linear-gradient(180deg, #0a0f17, #0a0e15);}
    .flake{width:6px; height:6px; border-radius:50%; background: radial-gradient(circle, #fff, rgba(255,255,255,.6)); filter: drop-shadow(0 0 6px rgba(255,255,255,.35)); opacity:.9; animation: snowFall linear infinite;}
    @keyframes snowFall{ to{ transform: translate3d(var(--x drift, 0px), 105vh, 0) rotate(360deg) } }

    /* 大风：绿色冷调 + 叶片/粒子横向吹拂 */
    #wind-layer{background: radial-gradient(900px 700px at 80% -10%, rgba(159,230,160,.20), rgba(159,230,160,0) 60%), linear-gradient(180deg, #0a120f, #0a100e);}
    .leaf{width:10px; height:4px; border-radius:8px; background: linear-gradient(90deg, rgba(223,255,226,.95), rgba(159,230,160,.8)); opacity:.85; transform-origin: center; animation: windBlow linear infinite;}
    @keyframes windBlow{ to{ transform: translate3d(110vw, var(--vy,0px),0) rotate(720deg)} }

    /* 中央新拟态卡片 */
    .center{position:relative; z-index:2; display:grid; place-items:center; min-height:100vh; padding:24px}
    .card{
      width:min(92vw, 440px); border-radius:24px; padding:24px 24px 20px;
      background: linear-gradient(180deg, rgba(255,255,255,.035), rgba(255,255,255,.015)), var(--card);
      /* 新拟态外阴影（深色基底） */
      box-shadow: 24px 24px 48px rgba(0,0,0,.55), -24px -24px 48px rgba(255,255,255,.04);
      transition: box-shadow .35s ease, transform .35s ease, background .35s ease;
    }
    .card::after{ /* 细微内高光 */
      content:""; position:absolute; inset:1px; border-radius:24px; pointer-events:none;
      box-shadow: inset 0 1px 0 rgba(255,255,255,.06);
    }
    .city{font-weight:600; letter-spacing:.3px; color:var(--fg); opacity:.95; font-size: clamp(14px, 2vw, 16px)}
    .temp{font-size: clamp(44px, 8vw, 64px); line-height:1; font-weight:700; margin:10px 0; background: linear-gradient(90deg, var(--accent), var(--accent-2)); -webkit-background-clip:text; background-clip:text; color:transparent;}
    .desc{color:var(--muted); font-size: clamp(14px, 2.2vw, 16px)}
    .divider{height:1px; background: linear-gradient(90deg, transparent, rgba(255,255,255,.12), transparent); margin:18px 0}
    .meta{display:flex; gap:12px; flex-wrap:wrap}
    .chip{padding:8px 10px; border-radius:999px; font-size:12px; color:#cdd3de; background: linear-gradient(180deg, rgba(255,255,255,.04), rgba(255,255,255,.02)); box-shadow: 8px 8px 16px rgba(0,0,0,.35), -8px -8px 16px rgba(255,255,255,.03);}

    /* 控制按钮（毛玻璃+新拟态融合） */
    .controls{position:fixed; left:50%; transform:translateX(-50%); bottom:24px; z-index:3; display:flex; gap:12px; padding:12px; border-radius:999px; background:rgba(20,24,32,.55); backdrop-filter: blur(12px); box-shadow: 18px 18px 36px rgba(0,0,0,.55), -18px -18px 36px rgba(255,255,255,.04);}
    .mode-btn{width:48px; height:48px; display:grid; place-items:center; border:none; border-radius:14px; cursor:pointer; color:#dfe6f1; background: linear-gradient(180deg, rgba(255,255,255,.06), rgba(255,255,255,.02)); box-shadow: 10px 10px 20px rgba(0,0,0,.45), -10px -10px 20px rgba(255,255,255,.03); transition: transform .2s ease, box-shadow .2s ease, background .3s ease, color .3s ease;}
    .mode-btn:hover{transform: translateY(-2px)}
    .mode-btn:active{transform: translateY(0); box-shadow: inset 6px 6px 12px rgba(0,0,0,.35), inset -6px -6px 12px rgba(255,255,255,.03)}
    .mode-btn[aria-pressed="true"]{outline:2px solid rgba(255,255,255,.08); color:var(--accent); background: linear-gradient(180deg, rgba(255,255,255,.08), rgba(255,255,255,.03));}
    .mode-btn:focus-visible{outline:2px solid var(--accent)}
    .mode-btn svg{width:22px; height:22px;}

    /* 风格切换按钮组 */
    .style-switch{position:fixed; right:24px; bottom:24px; z-index:3; display:flex; gap:8px; padding:10px; border-radius:12px; background:rgba(20,24,32,.45); backdrop-filter: blur(10px); box-shadow: 16px 16px 32px rgba(0,0,0,.5), -16px -16px 32px rgba(255,255,255,.03);}
    .style-btn{padding:8px 12px; border:none; border-radius:10px; cursor:pointer; color:#dfe6f1; background: linear-gradient(180deg, rgba(255,255,255,.06), rgba(255,255,255,.02)); box-shadow: 8px 8px 16px rgba(0,0,0,.45), -8px -8px 16px rgba(255,255,255,.03); font-size:12px; letter-spacing:.2px; transition: all .25s ease}
    .style-btn[aria-pressed="true"]{color:var(--accent); outline:2px solid rgba(255,255,255,.08)}

    @media (max-width:520px){ .controls{bottom:16px; gap:10px; padding:10px} .mode-btn{width:44px; height:44px} .style-switch{right:12px; bottom:12px; padding:8px} }

    /* 毛玻璃风格覆盖（当 body[data-style="glass"] 启用时） */
    body[data-style="glass"] .card{ background: rgba(255,255,255,.06); box-shadow: 0 12px 48px rgba(0,0,0,.45); backdrop-filter: blur(14px) saturate(120%); }
    body[data-style="glass"] .chip{ background: rgba(255,255,255,.05); box-shadow: none }

    /* 减少动态偏好处理 */
    @media (prefers-reduced-motion: reduce){
      .layer .particle{animation-duration: 15s !important}
      .layer{transition:none}
      .mode-btn{transition:none}
    }
  </style>
</head>
<body data-mode="sunny">
  <div class="app">
    <!-- 全屏动态背景层：预创建并通过 .active 切换可见性，避免切换延迟 -->
    <div class="scene" aria-hidden="true">
      <div id="sunny-layer" class="layer active">
        <div class="sun"></div>
        <div id="clouds"></div>
      </div>
      <div id="rain-layer" class="layer"></div>
      <div id="snow-layer" class="layer"></div>
      <div id="wind-layer" class="layer"></div>
    </div>

    <!-- 中央天气卡片（新拟态） -->
    <main class="center">
      <section class="card" role="region" aria-label="天气信息">
        <div class="city" id="city">杭州</div>
        <div class="temp" id="temp">25°C</div>
        <div class="desc" id="desc">局部多云</div>
        <div class="divider"></div>
        <div class="meta">
          <div class="chip" id="chip1">体感 26°C</div>
          <div class="chip" id="chip2">湿度 60%</div>
          <div class="chip" id="chip3">风速 5 m/s</div>
        </div>
      </section>
    </main>

    <!-- 天气模式切换控件 -->
    <nav class="controls" aria-label="天气模式切换">
      <button class="mode-btn" data-mode="sunny" aria-pressed="true" title="晴天">
        <!-- 太阳/云 图标 -->
        <svg viewBox="0 0 24 24" fill="currentColor" aria-hidden="true"><path d="M6 14a4 4 0 1 1 6.9-2.5h.1A3 3 0 1 1 15 17H7.5A3.5 3.5 0 1 1 9 10.2 4 4 0 0 1 6 14Z"/></svg>
      </button>
      <button class="mode-btn" data-mode="rain" aria-pressed="false" title="雨天">
        <!-- 雨滴 图标 -->
        <svg viewBox="0 0 24 24" fill="currentColor" aria-hidden="true"><path d="M7 10c0-3 5-7 5-7s5 4 5 7a5 5 0 0 1-10 0Zm1 9c0 .6-.4 1-1 1s-1-.4-1-1 .4-1 1-1 1 .4 1 1Zm5 2c0 .6-.4 1-1 1s-1-.4-1-1 .4-1 1-1 1 .4 1 1Zm5-2c0 .6-.4 1-1 1s-1-.4-1-1 .4-1 1-1 1 .4 1 1Z"/></svg>
      </button>
      <button class="mode-btn" data-mode="snow" aria-pressed="false" title="下雪">
        <!-- 雪花 图标 -->
        <svg viewBox="0 0 24 24" fill="currentColor" aria-hidden="true"><path d="M11 2h2v20h-2V2Zm7.1 3.5 1.4 1.4-14.1 14-1.4-1.4 14.1-14ZM2 11h20v2H2v-2Z" opacity=".6"/></svg>
      </button>
      <button class="mode-btn" data-mode="wind" aria-pressed="false" title="大风">
        <!-- 风 图标 -->
        <svg viewBox="0 0 24 24" fill="currentColor" aria-hidden="true"><path d="M3 10h10a3 3 0 1 0-3-3h2a1 1 0 1 1 1-1 1 1 0 0 1-1 1H3v3Zm0 4h14a3 3 0 1 1-3 3h2a1 1 0 1 0 1 1 1 1 0 0 0-1-1H3v-3Z"/></svg>
      </button>
    </nav>

    <!-- 风格切换（新拟态/毛玻璃） -->
    <div class="style-switch" role="group" aria-label="界面风格切换">
      <button class="style-btn" data-style="neumorphism" aria-pressed="true">新拟态</button>
      <button class="style-btn" data-style="glass" aria-pressed="false">毛玻璃</button>
    </div>
  </div>

  <script>
    // 单文件纯原生实现；粒子层预创建，切换时仅切换可见性和动画播放状态 —— Confirmed via 寸止
    const MODE_DESC = {
      sunny: { temp:'25°C', desc:'局部多云', chip1:'体感 26°C', chip2:'湿度 52%', chip3:'风速 3 m/s'},
      rain:  { temp:'18°C', desc:'小到中雨', chip1:'体感 18°C', chip2:'湿度 88%', chip3:'风速 4 m/s'},
      snow:  { temp:'-2°C', desc:'小雪飘落', chip1:'体感 -4°C', chip2:'湿度 72%', chip3:'风速 2 m/s'},
      wind:  { temp:'12°C', desc:'大风天气', chip1:'阵风 15 m/s', chip2:'湿度 40%', chip3:'能见度 良好'}
    };

    const els = {
      body: document.body,
      btns: Array.from(document.querySelectorAll('.mode-btn')),
      styleBtns: null,
      layers: {
        sunny: document.getElementById('sunny-layer'),
        rain:  document.getElementById('rain-layer'),
        snow:  document.getElementById('snow-layer'),
        wind:  document.getElementById('wind-layer'),
      },
      city: document.getElementById('city'),
      temp: document.getElementById('temp'),
      desc: document.getElementById('desc'),
      chip1: document.getElementById('chip1'),
      chip2: document.getElementById('chip2'),
      chip3: document.getElementById('chip3'),
      clouds: document.getElementById('clouds')
    };

    function setActiveLayer(mode){
      for(const [k,layer] of Object.entries(els.layers)){
        layer.classList.toggle('active', k===mode);
      }
    }

    function applyMode(mode){
      els.body.setAttribute('data-mode', mode);
      setActiveLayer(mode);
      // 更新卡片信息
      const d = MODE_DESC[mode];
      els.temp.textContent = d.temp; els.desc.textContent = d.desc; els.chip1.textContent = d.chip1; els.chip2.textContent = d.chip2; els.chip3.textContent = d.chip3;
      // 更新按钮状态与无障碍属性
      els.btns.forEach(b=> b.setAttribute('aria-pressed', String(b.dataset.mode===mode)));
    }

    function applyStyle(style){
      document.body.setAttribute('data-style', style);
      if(!els.styleBtns) return;
      els.styleBtns.forEach(b=> b.setAttribute('aria-pressed', String(b.dataset.style===style)));
    }

    function rand(min,max){ return Math.random()*(max-min)+min }
    function randInt(min,max){ return Math.floor(rand(min,max+1)) }

    // 创建晴天云
    function createClouds(count){
      const h = window.innerHeight, w = window.innerWidth;
      for(let i=0;i<count;i++){
        const c = document.createElement('div'); c.className='cloud';
        const cw = rand(120, 320), ch = rand(28, 60); c.style.width=cw+'px'; c.style.height=ch+'px';
        c.style.top = rand(6, 40)+'vh'; c.style.left = rand(-10, 70)+'vw';
        c.style.animationDuration = rand(30, 60)+'s'; c.style.animationDelay = (-1*rand(0,60))+'s';
        // 复合云瓣
        c.style.boxShadow = `${cw*0.4}px ${ch*0.2}px ${Math.max(30, ch*1)}px rgba(0,0,0,.12) inset`;
        els.clouds.appendChild(c);
      }
    }

    // 创建雨滴
    function createRain(count){
      const cont = els.layers.rain;
      for(let i=0;i<count;i++){
        const d = document.createElement('div'); d.className='drop particle';
        const left = rand(-5, 100); const delay = rand(0, 1.2); const dur = rand(0.8, 1.6);
        const length = rand(40, 110); d.style.height = length+'px';
        d.style.left = left+'vw'; d.style.top = (-rand(0, 100))+'vh';
        d.style.opacity = rand(0.35, .9);
        d.style.animationDuration = dur+'s'; d.style.animationDelay = (-delay)+'s';
        cont.appendChild(d);
      }
    }

    // 创建雪花
    function createSnow(count){
      const cont = els.layers.snow;
      for(let i=0;i<count;i++){
        const f = document.createElement('div'); f.className='flake particle';
        const size = rand(3,8); f.style.width=size+'px'; f.style.height=size+'px';
        const left = rand(0,100); f.style.left = left+'vw'; f.style.top = (-rand(0, 100))+'vh';
        const dur = rand(8, 16); const delay = rand(0, 10);
        const drift = rand(-30, 30); f.style.setProperty('--x drift', drift+'px');
        f.style.animationDuration = dur+'s'; f.style.animationDelay = (-delay)+'s';
        f.style.opacity = rand(.5,.95);
        cont.appendChild(f);
      }
    }

    // 创建风叶/粒子
    function createWind(count){
      const cont = els.layers.wind;
      for(let i=0;i<count;i++){
        const l = document.createElement('div'); l.className='leaf particle';
        const top = rand(0, 100); const startX = -rand(0, 120);
        const dur = rand(6, 14); const delay = rand(0, 6);
        const vy = rand(-40, 40); l.style.setProperty('--vy', vy+'px');
        const w = rand(6, 14), h = rand(3, 6); l.style.width=w+'px'; l.style.height=h+'px';
        l.style.top = top+'vh'; l.style.left = startX+'vw';
        l.style.animationDuration = dur+'s'; l.style.animationDelay = (-delay)+'s';
        l.style.opacity = rand(.55, .95);
        cont.appendChild(l);
      }
    }

    // 自适应粒子密度（基于视窗面积）
    function getCounts(){
      const area = window.innerWidth * window.innerHeight;
      const base = Math.min(1.0, Math.max(.6, area / (1280*720)));
      return {
        clouds: Math.round(7 * base),     // 轻微增加云量
        rain: Math.round(180 * base),     // 提升雨滴数量
        snow: Math.round(140 * base),     // 提升雪花数量
        wind: Math.round(140 * base)      // 提升风粒子数量
      };
    }

    // 初始化
    function init(){
      const {clouds, rain, snow, wind} = getCounts();
      createClouds(clouds); createRain(rain); createSnow(snow); createWind(wind);
      // 绑定按钮
      els.btns.forEach(btn=>{
        btn.addEventListener('click', ()=> applyMode(btn.dataset.mode));
        btn.addEventListener('keydown', (e)=>{ if(e.key==='Enter' || e.key===' '){ e.preventDefault(); btn.click(); }});
      });
      // 风格按钮
      els.styleBtns = Array.from(document.querySelectorAll('.style-btn'));
      els.styleBtns.forEach(btn=>{
        btn.addEventListener('click', ()=> applyStyle(btn.dataset.style));
        btn.addEventListener('keydown', (e)=>{ if(e.key==='Enter' || e.key===' '){ e.preventDefault(); btn.click(); }});
      });
      window.addEventListener('resize', onResizeReflow);
    }

    // 窗口变化时，轻量重排：适度补齐或裁剪粒子数量，避免密度异常
    let resizeTimer=null;
    function onResizeReflow(){
      clearTimeout(resizeTimer);
      resizeTimer = setTimeout(()=>{
        const target = getCounts();
        adjustParticles(els.layers.rain, '.drop', target.rain, ()=>createRain(target.rain - countParticles(els.layers.rain,'.drop')));
        adjustParticles(els.layers.snow, '.flake', target.snow, ()=>createSnow(target.snow - countParticles(els.layers.snow,'.flake')));
        adjustParticles(els.layers.wind, '.leaf', target.wind, ()=>createWind(target.wind - countParticles(els.layers.wind,'.leaf')));
      }, 200);
    }
    function countParticles(container, selector){ return container.querySelectorAll(selector).length }
    function adjustParticles(container, selector, target, addFn){
      const curr = countParticles(container, selector);
      if(curr < target){ addFn(); }
      else if(curr > target){
        const remove = curr - target; let removed=0;
        Array.from(container.querySelectorAll(selector)).slice(0, remove).forEach(n=>{ n.remove(); removed++; });
      }
    }

    document.addEventListener('DOMContentLoaded', init);
  </script>
</body>
</html>