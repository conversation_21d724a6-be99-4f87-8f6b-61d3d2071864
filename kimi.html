<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级天气展示卡片</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            height: 100vh;
            overflow: hidden;
            background: #0a0a0a;
            transition: background 0.5s ease;
        }

        /* 天气背景容器 */
        .weather-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            opacity: 0;
            transition: opacity 0.5s ease;
        }

        .weather-background.active {
            opacity: 1;
        }

        /* 晴天背景 */
        .sunny-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .sunny-bg::before {
            content: '';
            position: absolute;
            top: 20%;
            left: 20%;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, #ffd700 0%, #ff8c00 100%);
            border-radius: 50%;
            box-shadow: 0 0 50px #ffd700;
            animation: sun-glow 3s ease-in-out infinite;
        }

        @keyframes sun-glow {
            0%, 100% { transform: scale(1); opacity: 0.8; }
            50% { transform: scale(1.1); opacity: 1; }
        }

        /* 雨天背景 */
        .rainy-bg {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }

        .rain-drop {
            position: absolute;
            width: 2px;
            height: 20px;
            background: linear-gradient(to bottom, transparent, #4fc3f7);
            animation: rain-fall linear infinite;
        }

        @keyframes rain-fall {
            to {
                transform: translateY(100vh);
            }
        }

        /* 下雪背景 */
        .snowy-bg {
            background: linear-gradient(135deg, #3c3c3c 0%, #5a5a5a 100%);
        }

        .snow-flake {
            position: absolute;
            color: white;
            font-size: 1em;
            animation: snow-fall linear infinite;
        }

        @keyframes snow-fall {
            to {
                transform: translateY(100vh) rotate(360deg);
            }
        }

        /* 大风背景 */
        .windy-bg {
            background: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
        }

        .leaf {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #00b894;
            border-radius: 0 100% 0 100%;
            animation: wind-blow linear infinite;
        }

        @keyframes wind-blow {
            to {
                transform: translateX(100vw) translateY(50px) rotate(360deg);
            }
        }

        /* 主卡片容器 */
        .card-container {
            position: relative;
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            padding: 20px;
        }

        /* 天气卡片 */
        .weather-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 30px;
            padding: 40px;
            width: 100%;
            max-width: 400px;
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .weather-card:hover {
            transform: translateY(-5px);
            box-shadow: 
                0 12px 40px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        /* 天气图标 */
        .weather-icon {
            font-size: 4em;
            text-align: center;
            margin-bottom: 20px;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        /* 天气信息 */
        .weather-info {
            text-align: center;
            color: white;
        }

        .temperature {
            font-size: 3em;
            font-weight: 300;
            margin-bottom: 10px;
        }

        .description {
            font-size: 1.2em;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .location {
            font-size: 1em;
            opacity: 0.6;
        }

        /* 控制按钮 */
        .controls {
            display: flex;
            gap: 10px;
            margin-top: 30px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .weather-btn {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
            backdrop-filter: blur(10px);
        }

        .weather-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .weather-btn.active {
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .weather-card {
                padding: 30px;
                margin: 20px;
            }

            .temperature {
                font-size: 2.5em;
            }

            .weather-icon {
                font-size: 3em;
            }

            .controls {
                gap: 8px;
            }

            .weather-btn {
                padding: 10px 20px;
                font-size: 0.8em;
            }
        }

        /* 加载动画 */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <!-- 天气背景 -->
    <div class="weather-background sunny-bg active" id="sunny-bg"></div>
    <div class="weather-background rainy-bg" id="rainy-bg"></div>
    <div class="weather-background snowy-bg" id="snowy-bg"></div>
    <div class="weather-background windy-bg" id="windy-bg"></div>

    <!-- 主卡片 -->
    <div class="card-container">
        <div class="weather-card fade-in">
            <div class="weather-icon" id="weather-icon">☀️</div>
            <div class="weather-info">
                <div class="temperature" id="temperature">25°C</div>
                <div class="description" id="description">局部多云</div>
                <div class="location" id="location">北京</div>
            </div>
            <div class="controls">
                <button class="weather-btn active" onclick="changeWeather('sunny')">晴天</button>
                <button class="weather-btn" onclick="changeWeather('rainy')">雨天</button>
                <button class="weather-btn" onclick="changeWeather('snowy')">下雪</button>
                <button class="weather-btn" onclick="changeWeather('windy')">大风</button>
            </div>
        </div>
    </div>

    <script>
        // 天气配置
        const weatherConfig = {
            sunny: {
                icon: '☀️',
                temperature: '25°C',
                description: '局部多云',
                location: '北京',
                bgClass: 'sunny-bg'
            },
            rainy: {
                icon: '🌧️',
                temperature: '18°C',
                description: '小雨',
                location: '上海',
                bgClass: 'rainy-bg'
            },
            snowy: {
                icon: '❄️',
                temperature: '-2°C',
                description: '小雪',
                location: '哈尔滨',
                bgClass: 'snowy-bg'
            },
            windy: {
                icon: '💨',
                temperature: '15°C',
                description: '大风',
                location: '内蒙古',
                bgClass: 'windy-bg'
            }
        };

        let currentWeather = 'sunny';
        let animationIntervals = [];

        // 创建雨滴
        function createRainDrops() {
            const rainyBg = document.getElementById('rainy-bg');
            for (let i = 0; i < 100; i++) {
                const drop = document.createElement('div');
                drop.className = 'rain-drop';
                drop.style.left = Math.random() * 100 + '%';
                drop.style.animationDuration = (Math.random() * 1 + 0.5) + 's';
                drop.style.animationDelay = Math.random() * 2 + 's';
                rainyBg.appendChild(drop);
            }
        }

        // 创建雪花
        function createSnowFlakes() {
            const snowyBg = document.getElementById('snowy-bg');
            const snowflakes = ['❄', '❅', '❆'];
            for (let i = 0; i < 50; i++) {
                const flake = document.createElement('div');
                flake.className = 'snow-flake';
                flake.textContent = snowflakes[Math.floor(Math.random() * snowflakes.length)];
                flake.style.left = Math.random() * 100 + '%';
                flake.style.animationDuration = (Math.random() * 3 + 2) + 's';
                flake.style.animationDelay = Math.random() * 3 + 's';
                flake.style.fontSize = (Math.random() * 10 + 10) + 'px';
                snowyBg.appendChild(flake);
            }
        }

        // 创建树叶
        function createLeaves() {
            const windyBg = document.getElementById('windy-bg');
            const colors = ['#00b894', '#00cec9', '#55a3ff', '#fdcb6e'];
            for (let i = 0; i < 30; i++) {
                const leaf = document.createElement('div');
                leaf.className = 'leaf';
                leaf.style.top = Math.random() * 100 + '%';
                leaf.style.animationDuration = (Math.random() * 5 + 3) + 's';
                leaf.style.animationDelay = Math.random() * 5 + 's';
                leaf.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                leaf.style.transform = `rotate(${Math.random() * 360}deg)`;
                windyBg.appendChild(leaf);
            }
        }

        // 切换天气
        function changeWeather(weather) {
            if (currentWeather === weather) return;

            // 更新按钮状态
            document.querySelectorAll('.weather-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // 隐藏当前背景
            document.querySelectorAll('.weather-background').forEach(bg => {
                bg.classList.remove('active');
            });

            // 显示新背景
            setTimeout(() => {
                document.getElementById(`${weather}-bg`).classList.add('active');
            }, 250);

            // 更新卡片内容
            const config = weatherConfig[weather];
            const card = document.querySelector('.weather-card');
            card.style.transform = 'scale(0.95)';
            card.style.opacity = '0.7';

            setTimeout(() => {
                document.getElementById('weather-icon').textContent = config.icon;
                document.getElementById('temperature').textContent = config.temperature;
                document.getElementById('description').textContent = config.description;
                document.getElementById('location').textContent = config.location;
                
                card.style.transform = 'scale(1)';
                card.style.opacity = '1';
            }, 300);

            currentWeather = weather;
        }

        // 初始化
        function init() {
            createRainDrops();
            createSnowFlakes();
            createLeaves();
            
            // 添加键盘支持
            document.addEventListener('keydown', (e) => {
                const weatherMap = {
                    '1': 'sunny',
                    '2': 'rainy',
                    '3': 'snowy',
                    '4': 'windy'
                };
                
                if (weatherMap[e.key]) {
                    const btn = document.querySelector(`[onclick="changeWeather('${weatherMap[e.key]}')"]`);
                    if (btn) {
                        btn.click();
                    }
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>