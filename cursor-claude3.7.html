<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>812所质量信息看板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
        }
        
        body {
            background-color: #f7f8fa;
            color: #333;
            overflow: hidden;
        }
        
        .container {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        /* 顶部区域 */
        .header {
            height: 80px;
            background: linear-gradient(90deg, #2B5EAD, #4A7BCA);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            font-size: 36px;
            font-weight: bold;
            letter-spacing: 2px;
            position: relative;
        }
        
        .header h1:before,
        .header h1:after {
            content: '';
            position: absolute;
            height: 3px;
            width: 60px;
            background-color: rgba(255, 255, 255, 0.7);
            top: 50%;
        }
        
        .header h1:before {
            left: -80px;
        }
        
        .header h1:after {
            right: -80px;
        }
        
        /* 主体区域 */
        .main {
            flex: 1;
            display: flex;
            flex-wrap: wrap;
            padding: 20px;
        }
        
        /* 模块通用样式 */
        .module {
            width: calc(50% - 20px);
            height: calc(50% - 20px);
            margin: 10px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .module:hover {
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            transform: translateY(-5px);
        }
        
        .module-header {
            height: 50px;
            background: linear-gradient(90deg, #f5f7fa, #e4e8f0);
            display: flex;
            align-items: center;
            padding: 0 20px;
            border-bottom: 1px solid #eee;
        }
        
        .module-title {
            font-size: 18px;
            font-weight: bold;
            color: #2B5EAD;
            position: relative;
            padding-left: 15px;
        }
        
        .module-title:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 18px;
            background-color: #2B5EAD;
            border-radius: 2px;
        }
        
        .module-content {
            flex: 1;
            padding: 20px;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
            align-items: center;
        }
        
        /* 卡片样式 */
        .card {
            width: calc(50% - 20px);
            height: calc(50% - 20px);
            min-height: 100px;
            background-color: #f9fafc;
            border-radius: 6px;
            margin: 10px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            transition: all 0.3s ease;
            border-left: 4px solid #4A7BCA;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .card:hover {
            transform: scale(1.03);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .card-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .card-value {
            font-size: 28px;
            font-weight: bold;
            color: #2B5EAD;
        }
        
        /* 不同模块卡片边框颜色 */
        #module1 .card {
            border-left-color: #4A7BCA;
        }
        
        #module1 .card .card-value {
            color: #4A7BCA;
        }
        
        #module2 .card {
            border-left-color: #67C23A;
        }
        
        #module2 .card .card-value {
            color: #67C23A;
        }
        
        #module3 .card {
            border-left-color: #E6A23C;
        }
        
        #module3 .card .card-value {
            color: #E6A23C;
        }
        
        #module4 .card {
            border-left-color: #F56C6C;
        }
        
        #module4 .card .card-value {
            color: #F56C6C;
        }
        
        /* 动画效果 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .module {
            animation: fadeIn 0.8s ease forwards;
        }
        
        #module1 {
            animation-delay: 0.1s;
        }
        
        #module2 {
            animation-delay: 0.3s;
        }
        
        #module3 {
            animation-delay: 0.5s;
        }
        
        #module4 {
            animation-delay: 0.7s;
        }
        
        /* 响应式设计 */
        @media (max-width: 1200px) {
            .module {
                width: calc(100% - 20px);
                height: calc(25% - 20px);
            }
            
            .card {
                width: calc(25% - 20px);
                height: calc(100% - 20px);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>812所质量信息看板</h1>
        </div>
        
        <div class="main">
            <!-- 现场问题监控模块 -->
            <div id="module1" class="module">
                <div class="module-header">
                    <div class="module-title">现场问题监控</div>
                </div>
                <div class="module-content">
                    <div class="card">
                        <div class="card-title">集团级不合格</div>
                        <div class="card-value">0</div>
                    </div>
                    <div class="card">
                        <div class="card-title">院级不合格</div>
                        <div class="card-value">2</div>
                    </div>
                    <div class="card">
                        <div class="card-title">所级不合格</div>
                        <div class="card-value">5</div>
                    </div>
                    <div class="card">
                        <div class="card-title">其他不合格</div>
                        <div class="card-value">3</div>
                    </div>
                </div>
            </div>
            
            <!-- 测试异常追踪模块 -->
            <div id="module2" class="module">
                <div class="module-header">
                    <div class="module-title">测试异常追踪</div>
                </div>
                <div class="module-content">
                    <div class="card">
                        <div class="card-title">集团级异常</div>
                        <div class="card-value">1</div>
                    </div>
                    <div class="card">
                        <div class="card-title">院级异常</div>
                        <div class="card-value">3</div>
                    </div>
                    <div class="card">
                        <div class="card-title">所级异常</div>
                        <div class="card-value">7</div>
                    </div>
                    <div class="card">
                        <div class="card-title">其他异常</div>
                        <div class="card-value">4</div>
                    </div>
                </div>
            </div>
            
            <!-- 不合格品审理分析模块 -->
            <div id="module3" class="module">
                <div class="module-header">
                    <div class="module-title">不合格品审理分析</div>
                </div>
                <div class="module-content">
                    <div class="card">
                        <div class="card-title">一级审理</div>
                        <div class="card-value">12</div>
                    </div>
                    <div class="card">
                        <div class="card-title">二级审理</div>
                        <div class="card-value">8</div>
                    </div>
                    <div class="card">
                        <div class="card-title">三级审理</div>
                        <div class="card-value">4</div>
                    </div>
                    <div class="card">
                        <div class="card-title">其他审理</div>
                        <div class="card-value">2</div>
                    </div>
                </div>
            </div>
            
            <!-- 现场临时处理监控模块 -->
            <div id="module4" class="module">
                <div class="module-header">
                    <div class="module-title">现场临时处理监控</div>
                </div>
                <div class="module-content">
                    <div class="card">
                        <div class="card-title">报警遥测波道屏蔽</div>
                        <div class="card-value">6</div>
                    </div>
                    <div class="card">
                        <div class="card-title">遥测遥控指令变更</div>
                        <div class="card-value">9</div>
                    </div>
                    <div class="card">
                        <div class="card-title">加载软件修改</div>
                        <div class="card-value">3</div>
                    </div>
                    <div class="card">
                        <div class="card-title">变更程序、增减项目</div>
                        <div class="card-value">5</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后执行
        window.onload = function() {
            // 初始化数据
            var data = {
                module1: {
                    "集团级不合格": 0,
                    "院级不合格": 2,
                    "所级不合格": 5,
                    "其他不合格": 3
                },
                module2: {
                    "集团级异常": 1,
                    "院级异常": 3,
                    "所级异常": 7,
                    "其他异常": 4
                },
                module3: {
                    "一级审理": 12,
                    "二级审理": 8,
                    "三级审理": 4,
                    "其他审理": 2
                },
                module4: {
                    "报警遥测波道屏蔽": 6,
                    "遥测遥控指令变更": 9,
                    "加载软件修改": 3,
                    "变更程序、增减项目": 5
                }
            };
            
            // 数字动画函数
            function animateNumber(element, finalValue) {
                var currentValue = 0;
                var duration = 1500; // 动画持续时间（毫秒）
                var startTime = null;
                
                function step(timestamp) {
                    if (!startTime) startTime = timestamp;
                    var progress = timestamp - startTime;
                    var percentage = Math.min(progress / duration, 1);
                    
                    // 使用缓动函数使动画更自然
                    var easeOutQuart = 1 - Math.pow(1 - percentage, 4);
                    currentValue = Math.floor(easeOutQuart * finalValue);
                    
                    element.textContent = currentValue;
                    
                    if (percentage < 1) {
                        window.requestAnimationFrame(step);
                    } else {
                        element.textContent = finalValue;
                    }
                }
                
                window.requestAnimationFrame(step);
            }
            
            // 为所有卡片添加数字动画
            function initCardAnimations() {
                // 延迟执行，等待模块动画完成
                setTimeout(function() {
                    var cards = document.querySelectorAll('.card');
                    
                    cards.forEach(function(card) {
                        var valueElement = card.querySelector('.card-value');
                        var titleElement = card.querySelector('.card-title');
                        var moduleId = card.closest('.module').id;
                        var title = titleElement.textContent;
                        var finalValue = data[moduleId][title];
                        
                        // 开始动画
                        animateNumber(valueElement, finalValue);
                    });
                }, 1000);
            }
            
            // 调整模块大小
            function resizeModules() {
                var main = document.querySelector('.main');
                var modules = document.querySelectorAll('.module');
                var windowWidth = window.innerWidth;
                
                if (windowWidth < 1200) {
                    main.style.flexDirection = 'column';
                } else {
                    main.style.flexDirection = 'row';
                }
            }
            
            // 初始化
            initCardAnimations();
            resizeModules();
            
            // 窗口大小改变时重新调整布局
            window.addEventListener('resize', resizeModules);
        };
    </script>
</body>
</html>
