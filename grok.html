<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>812所质量信息看板</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: linear-gradient(to bottom, #e6f7ff, #f0f5ff);
            color: #333;
            overflow: hidden;
        }

        header {
            text-align: center;
            padding: 20px;
            background-color: rgba(255, 255, 255, 0.8);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        header h1 {
            margin: 0;
            font-size: 36px;
            color: #007bff;
            animation: fadeIn 1s ease-in-out;
        }

        main {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            height: calc(100vh - 80px);
            padding: 20px;
            gap: 20px;
        }

        section {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
            display: flex;
            flex-direction: column;
            animation: slideUp 0.8s ease-out;
        }

        section h2 {
            margin: 0 0 20px 0;
            font-size: 24px;
            text-align: center;
            color: #0056b3;
        }

        .cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            flex-grow: 1;
        }

        .card {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }

        .card-label {
            font-size: 16px;
            color: #666;
            margin-bottom: 10px;
        }

        .card-value {
            font-size: 32px;
            font-weight: bold;
            color: #007bff;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }

        @keyframes slideUp {
            from {
                transform: translateY(50px);
                opacity: 0;
            }

            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
    </style>
</head>

<body>
    <header>
        <h1>812所质量信息看板</h1>
    </header>
    <main>
        <!-- 左上: 现场问题监控 -->
        <section id="problem-monitoring">
            <h2>现场问题监控</h2>
            <div class="cards">
                <div class="card">
                    <div class="card-label">集团不合格问题</div>
                    <div class="card-value" data-target="15">0</div>
                </div>
                <div class="card">
                    <div class="card-label">院级不合格问题</div>
                    <div class="card-value" data-target="8">0</div>
                </div>
                <div class="card">
                    <div class="card-label">所级不合格问题</div>
                    <div class="card-value" data-target="12">0</div>
                </div>
                <div class="card">
                    <div class="card-label">其他不合格问题</div>
                    <div class="card-value" data-target="5">0</div>
                </div>
            </div>
        </section>

        <!-- 右上: 测试异常追踪 -->
        <section id="test-anomaly">
            <h2>测试异常追踪</h2>
            <div class="cards">
                <div class="card">
                    <div class="card-label">集团测试异常</div>
                    <div class="card-value" data-target="10">0</div>
                </div>
                <div class="card">
                    <div class="card-label">院级测试异常</div>
                    <div class="card-value" data-target="7">0</div>
                </div>
                <div class="card">
                    <div class="card-label">所级测试异常</div>
                    <div class="card-value" data-target="9">0</div>
                </div>
                <div class="card">
                    <div class="card-label">其他测试异常</div>
                    <div class="card-value" data-target="4">0</div>
                </div>
            </div>
        </section>

        <!-- 左下: 不合格品审理分析 -->
        <section id="non-qualified-review">
            <h2>不合格品审理分析</h2>
            <div class="cards">
                <div class="card">
                    <div class="card-label">一级审理</div>
                    <div class="card-value" data-target="20">0</div>
                </div>
                <div class="card">
                    <div class="card-label">二级审理</div>
                    <div class="card-value" data-target="14">0</div>
                </div>
                <div class="card">
                    <div class="card-label">三级审理</div>
                    <div class="card-value" data-target="11">0</div>
                </div>
                <div class="card">
                    <div class="card-label">其他不合格品</div>
                    <div class="card-value" data-target="6">0</div>
                </div>
            </div>
        </section>

        <!-- 右下: 现场临时处理监控 -->
        <section id="temporary-handling">
            <h2>现场临时处理监控</h2>
            <div class="cards">
                <div class="card">
                    <div class="card-label">报警遥测波道屏蔽</div>
                    <div class="card-value" data-target="3">0</div>
                </div>
                <div class="card">
                    <div class="card-label">遥测遥控指令变更</div>
                    <div class="card-value" data-target="5">0</div>
                </div>
                <div class="card">
                    <div class="card-label">加载软件修改</div>
                    <div class="card-value" data-target="2">0</div>
                </div>
                <div class="card">
                    <div class="card-label">变更程序、增减项目</div>
                    <div class="card-value" data-target="4">0</div>
                </div>
            </div>
        </section>
    </main>
    <script>
        (function () {
            var values = document.querySelectorAll('.card-value');
            var duration = 2000; // 动画持续时间(ms)

            function animateValue(element, start, end, duration) {
                var range = end - start;
                var current = start;
                var increment = end > start ? 1 : -1;
                var stepTime = Math.abs(Math.floor(duration / range));
                var timer = setInterval(function () {
                    current += increment;
                    element.textContent = current;
                    if (current === end) {
                        clearInterval(timer);
                    }
                }, stepTime);
            }

            values.forEach(function (value) {
                var target = parseInt(value.getAttribute('data-target'), 10);
                animateValue(value, 0, target, duration);
            });
        })();
    </script>
</body>

</html>