<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>812所质量信息看板</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" rel="stylesheet">
  <style>
    @font-face {
      font-family: 'MiSans';
      src: url('https://assets-persist.lovart.ai/agent-static-assets/MiSans-Medium.ttf') format('truetype');
      font-weight: 500;
    }

    @font-face {
      font-family: 'MiSans';
      src: url('https://assets-persist.lovart.ai/agent-static-assets/MiSans-Bold.ttf') format('truetype');
      font-weight: 700;
    }

    @font-face {
      font-family: 'MiSans';
      src: url('https://assets-persist.lovart.ai/agent-static-assets/MiSans-Regular.ttf') format('truetype');
      font-weight: 400;
    }

    @font-face {
      font-family: 'AlimamaShuHeiTi';
      src: url('https://assets-persist.lovart.ai/agent-static-assets/AlimamaShuHeiTi-Bold.otf') format('opentype');
      font-weight: 700;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'MiSans', sans-serif;
    }

    body {
      width: 1920px;
      background: linear-gradient(135deg, #f0f8ff 0%, #e6f2ff 100%);
      color: #333;
      overflow: hidden;
      position: relative;
    }

    body::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, rgba(0, 153, 255, 0.03) 0%, rgba(0, 102, 204, 0.05) 100%);
      z-index: -1;
    }

    .container {
      width: 1920px;
      min-height: 100vh;
      padding: 20px 40px 40px;
      display: flex;
      flex-direction: column;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 0;
      margin-bottom: 30px;
      position: relative;
    }

    .header::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1px;
      background: linear-gradient(to right, transparent, rgba(0, 153, 255, 0.5), transparent);
    }

    .title {
      font-family: 'AlimamaShuHeiTi', sans-serif;
      font-size: 42px;
      font-weight: 700;
      color: #0066cc;
      text-align: center;
      letter-spacing: 2px;
      flex-grow: 1;
      text-shadow: 0 2px 4px rgba(0, 102, 204, 0.1);
      opacity: 0;
      animation: fadeIn 1s ease forwards;
    }

    .datetime {
      font-size: 18px;
      color: #0066cc;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      opacity: 0;
      animation: fadeIn 1s ease 0.3s forwards;
    }

    .grid-container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-template-rows: 1fr 1fr;
      gap: 30px;
      flex-grow: 1;
    }

    .card {
      background: rgba(255, 255, 255, 0.8);
      border-radius: 12px;
      box-shadow: 0 8px 20px rgba(0, 102, 204, 0.1);
      overflow: hidden;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      display: flex;
      flex-direction: column;
      opacity: 0;
      animation: fadeIn 0.8s ease forwards;
    }

    .card:nth-child(1) {
      animation-delay: 0.4s;
    }

    .card:nth-child(2) {
      animation-delay: 0.6s;
    }

    .card:nth-child(3) {
      animation-delay: 0.8s;
    }

    .card:nth-child(4) {
      animation-delay: 1s;
    }

    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 12px 28px rgba(0, 102, 204, 0.15);
    }

    .card-header {
      background: linear-gradient(90deg, #0080ff 0%, #0066cc 100%);
      color: white;
      padding: 15px 20px;
      font-size: 20px;
      font-weight: 500;
      display: flex;
      align-items: center;
    }

    .card-header i {
      margin-right: 10px;
      font-size: 22px;
    }

    .card-body {
      padding: 20px;
      flex-grow: 1;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-gap: 20px;
    }

    .data-item {
      background: white;
      border-radius: 8px;
      padding: 15px;
      box-shadow: 0 4px 12px rgba(0, 102, 204, 0.08);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .data-item:hover {
      transform: scale(1.03);
      box-shadow: 0 6px 16px rgba(0, 102, 204, 0.12);
    }

    .data-item::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: linear-gradient(to bottom, #0080ff, #00b3ff);
    }

    .item-label {
      font-size: 16px;
      color: #555;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .item-value {
      font-size: 32px;
      font-weight: 700;
      color: #0066cc;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
    }

    .warning .item-value {
      color: #ff6600;
    }

    .critical .item-value {
      color: #ff3300;
    }

    .normal .item-value {
      color: #00cc66;
    }

    .item-value i {
      margin-right: 5px;
      font-size: 24px;
    }

    .pulse {
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0% {
        transform: scale(1);
      }

      50% {
        transform: scale(1.05);
      }

      100% {
        transform: scale(1);
      }
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes countUp {
      from {
        opacity: 0;
        transform: translateY(10px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .grid-4 {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
      gap: 20px;
      height: 100%;
    }

    .decoration {
      position: absolute;
      opacity: 0.05;
      z-index: -1;
    }

    .decoration-1 {
      top: 10%;
      left: 5%;
      width: 300px;
      height: 300px;
      border: 2px solid #0066cc;
      border-radius: 50%;
      animation: rotate 60s linear infinite;
    }

    .decoration-2 {
      bottom: 15%;
      right: 8%;
      width: 200px;
      height: 200px;
      border: 2px solid #0080ff;
      border-radius: 50%;
      animation: rotate 40s linear infinite reverse;
    }

    @keyframes rotate {
      from {
        transform: rotate(0deg);
      }

      to {
        transform: rotate(360deg);
      }
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="header">
      <div class="title">812所质量信息看板</div>
      <div class="datetime">
        <div class="date" id="date"></div>
        <div class="time" id="time"></div>
      </div>
    </div>

    <div class="grid-container">
      <!-- 现场问题监控 -->
      <div class="card">
        <div class="card-header">
          <i class="ri-alert-line"></i>
          现场问题监控
        </div>
        <div class="card-body">
          <div class="data-item warning">
            <div class="item-label">集团级不合格问题</div>
            <div class="item-value" data-value="18">0</div>
          </div>
          <div class="data-item critical">
            <div class="item-label">院级不合格问题</div>
            <div class="item-value" data-value="32">0</div>
          </div>
          <div class="data-item">
            <div class="item-label">所级不合格问题</div>
            <div class="item-value" data-value="47">0</div>
          </div>
          <div class="data-item normal">
            <div class="item-label">其他不合格问题</div>
            <div class="item-value" data-value="24">0</div>
          </div>
        </div>
      </div>

      <!-- 测试异常追踪 -->
      <div class="card">
        <div class="card-header">
          <i class="ri-error-warning-line"></i>
          测试异常追踪
        </div>
        <div class="card-body">
          <div class="data-item warning">
            <div class="item-label">集团级测试异常</div>
            <div class="item-value" data-value="12">0</div>
          </div>
          <div class="data-item critical">
            <div class="item-label">院级测试异常</div>
            <div class="item-value pulse" data-value="28">0</div>
          </div>
          <div class="data-item">
            <div class="item-label">所级测试异常</div>
            <div class="item-value" data-value="39">0</div>
          </div>
          <div class="data-item normal">
            <div class="item-label">其他测试异常</div>
            <div class="item-value" data-value="16">0</div>
          </div>
        </div>
      </div>

      <!-- 不合格品审理分析 -->
      <div class="card">
        <div class="card-header">
          <i class="ri-file-list-3-line"></i>
          不合格品审理分析
        </div>
        <div class="card-body">
          <div class="data-item">
            <div class="item-label">一级审理不合格品</div>
            <div class="item-value" data-value="42">0</div>
          </div>
          <div class="data-item warning">
            <div class="item-label">二级审理不合格品</div>
            <div class="item-value" data-value="27">0</div>
          </div>
          <div class="data-item critical">
            <div class="item-label">三级审理不合格品</div>
            <div class="item-value pulse" data-value="15">0</div>
          </div>
          <div class="data-item normal">
            <div class="item-label">其他不合格品</div>
            <div class="item-value" data-value="31">0</div>
          </div>
        </div>
      </div>

      <!-- 现场临时处理监控 -->
      <div class="card">
        <div class="card-header">
          <i class="ri-tools-line"></i>
          现场临时处理监控
        </div>
        <div class="card-body grid-4">
          <div class="data-item warning">
            <div class="item-label">报警遥测波道屏蔽</div>
            <div class="item-value" data-value="23">0</div>
          </div>
          <div class="data-item">
            <div class="item-label">遥测遥控指令变更</div>
            <div class="item-value" data-value="19">0</div>
          </div>
          <div class="data-item critical">
            <div class="item-label">加载软件修改</div>
            <div class="item-value pulse" data-value="8">0</div>
          </div>
          <div class="data-item normal">
            <div class="item-label">变更程序、增减项目</div>
            <div class="item-value" data-value="14">0</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="decoration decoration-1"></div>
  <div class="decoration decoration-2"></div>

  <script>
    // 更新日期和时间
    function updateDateTime() {
      var now = new Date();
      var dateOptions = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
      var timeOptions = { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false };

      document.getElementById('date').textContent = now.toLocaleDateString('zh-CN', dateOptions);
      document.getElementById('time').textContent = now.toLocaleTimeString('zh-CN', timeOptions);
    }

    // 初始化并定时更新日期时间
    updateDateTime();
    setInterval(updateDateTime, 1000);

    // 数字动画效果
    function animateNumbers() {
      var valueElements = document.querySelectorAll('.item-value');

      valueElements.forEach(function (element) {
        var finalValue = parseInt(element.getAttribute('data-value'));
        var duration = 2000; // 动画持续时间（毫秒）
        var startTime = null;

        function step(timestamp) {
          if (!startTime) startTime = timestamp;
          var progress = timestamp - startTime;
          var percentage = Math.min(progress / duration, 1);

          // 使用缓动函数使动画更自然
          var easeOutQuart = 1 - Math.pow(1 - percentage, 4);
          var currentValue = Math.floor(easeOutQuart * finalValue);

          element.textContent = currentValue;

          if (percentage < 1) {
            window.requestAnimationFrame(step);
          }
        }

        // 延迟启动动画，与卡片淡入动画配合
        setTimeout(function () {
          window.requestAnimationFrame(step);
        }, 1000);
      });
    }

    // 页面加载完成后执行动画
    window.addEventListener('load', function () {
      animateNumbers();
    });

    // 模拟数据更新
    function simulateDataUpdate() {
      var valueElements = document.querySelectorAll('.item-value');

      valueElements.forEach(function (element) {
        var currentValue = parseInt(element.textContent);
        var change = Math.floor(Math.random() * 5) - 2; // -2 到 +2 的随机变化
        var newValue = Math.max(0, currentValue + change);

        // 创建一个临时元素用于动画
        var tempElement = document.createElement('span');
        tempElement.textContent = newValue;
        tempElement.style.position = 'absolute';
        tempElement.style.opacity = '0';

        // 添加动画类
        element.classList.add('updating');

        // 更新值
        setTimeout(function () {
          element.textContent = newValue;
          element.classList.remove('updating');
        }, 300);
      });
    }

    // 每隔一段时间模拟数据更新
    setInterval(simulateDataUpdate, 30000);
  </script>
</body>

</html>