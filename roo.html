<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级天气展示卡片</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f, #1a1a1a);
            color: #fff;
            overflow: hidden;
            height: 100vh;
            position: relative;
        }

        .weather-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .weather-card {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 2;
            transition: all 0.5s ease;
        }

        .temperature {
            font-size: 4rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .description {
            font-size: 1.5rem;
            margin-bottom: 20px;
            opacity: 0.8;
        }

        .city {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.6;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #fff;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .control-btn.active {
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
        }

        /* 动画容器 */
        .animation-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
        }

        /* 雨天动画 */
        .rain-drop {
            position: absolute;
            background: linear-gradient(to bottom, transparent, #4a9eff);
            width: 2px;
            height: 20px;
            animation: fall 1s linear infinite;
        }

        @keyframes fall {
            to {
                transform: translateY(100vh);
                opacity: 0;
            }
        }

        /* 下雪动画 */
        .snow-flake {
            position: absolute;
            background: #fff;
            border-radius: 50%;
            animation: snow 3s linear infinite;
        }

        @keyframes snow {
            to {
                transform: translateY(100vh) rotate(360deg);
                opacity: 0;
            }
        }

        /* 大风动画 */
        .wind-particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            animation: wind 2s ease-in-out infinite;
        }

        @keyframes wind {
            0% {
                transform: translateX(0) translateY(0);
                opacity: 0;
            }
            50% {
                opacity: 1;
            }
            100% {
                transform: translateX(200px) translateY(-50px);
                opacity: 0;
            }
        }

        /* 晴天太阳 */
        .sun {
            position: absolute;
            top: 20%;
            left: 20%;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, #ffd700, #ff8c00);
            border-radius: 50%;
            box-shadow: 0 0 20px #ffd700;
            animation: shine 4s ease-in-out infinite;
        }

        @keyframes shine {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }

        /* 云朵 */
        .cloud {
            position: absolute;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            animation: drift 10s linear infinite;
        }

        .cloud:nth-child(1) {
            width: 150px;
            height: 80px;
            top: 30%;
            left: -150px;
            animation-delay: 0s;
        }

        .cloud:nth-child(2) {
            width: 120px;
            height: 60px;
            top: 50%;
            left: -120px;
            animation-delay: 5s;
        }

        @keyframes drift {
            to {
                transform: translateX(100vw);
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .weather-card {
                padding: 20px;
                width: 90%;
                max-width: 400px;
            }

            .temperature {
                font-size: 3rem;
            }

            .description {
                font-size: 1.2rem;
            }

            .controls {
                flex-direction: column;
                gap: 10px;
            }

            .control-btn {
                padding: 8px 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="weather-container">
        <div class="animation-container" id="animation-container"></div>
        <div class="weather-card">
            <div class="temperature" id="temperature">25°C</div>
            <div class="description" id="description">局部多云</div>
            <div class="city" id="city">北京</div>
            <div class="controls">
                <button class="control-btn active" data-weather="sunny">晴天</button>
                <button class="control-btn" data-weather="rainy">雨天</button>
                <button class="control-btn" data-weather="snowy">下雪</button>
                <button class="control-btn" data-weather="windy">大风</button>
            </div>
        </div>
    </div>

    <script>
        const weatherCard = document.querySelector('.weather-card');
        const animationContainer = document.getElementById('animation-container');
        const controlButtons = document.querySelectorAll('.control-btn');
        const temperature = document.getElementById('temperature');
        const description = document.getElementById('description');
        const city = document.getElementById('city');

        const weatherData = {
            sunny: { temp: '25°C', desc: '晴朗', city: '北京', bg: 'linear-gradient(135deg, #87ceeb, #ffd700)' },
            rainy: { temp: '18°C', desc: '大雨', city: '上海', bg: 'linear-gradient(135deg, #4a5568, #2d3748)' },
            snowy: { temp: '-5°C', desc: '大雪', city: '哈尔滨', bg: 'linear-gradient(135deg, #e2e8f0, #cbd5e0)' },
            windy: { temp: '15°C', desc: '大风', city: '兰州', bg: 'linear-gradient(135deg, #a0aec0, #718096)' }
        };

        function clearAnimations() {
            animationContainer.innerHTML = '';
        }

        function createRain() {
            for (let i = 0; i < 100; i++) {
                const drop = document.createElement('div');
                drop.classList.add('rain-drop');
                drop.style.left = Math.random() * 100 + '%';
                drop.style.animationDuration = (Math.random() * 0.5 + 0.5) + 's';
                drop.style.animationDelay = Math.random() * 2 + 's';
                animationContainer.appendChild(drop);
            }
        }

        function createSnow() {
            for (let i = 0; i < 50; i++) {
                const flake = document.createElement('div');
                flake.classList.add('snow-flake');
                flake.style.width = flake.style.height = (Math.random() * 5 + 2) + 'px';
                flake.style.left = Math.random() * 100 + '%';
                flake.style.animationDuration = (Math.random() * 2 + 2) + 's';
                flake.style.animationDelay = Math.random() * 3 + 's';
                animationContainer.appendChild(flake);
            }
        }

        function createWind() {
            for (let i = 0; i < 30; i++) {
                const particle = document.createElement('div');
                particle.classList.add('wind-particle');
                particle.style.width = particle.style.height = (Math.random() * 8 + 2) + 'px';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 2 + 's';
                animationContainer.appendChild(particle);
            }
        }

        function createSunny() {
            const sun = document.createElement('div');
            sun.classList.add('sun');
            animationContainer.appendChild(sun);

            const cloud1 = document.createElement('div');
            cloud1.classList.add('cloud');
            animationContainer.appendChild(cloud1);

            const cloud2 = document.createElement('div');
            cloud2.classList.add('cloud');
            animationContainer.appendChild(cloud2);
        }

        function switchWeather(weather) {
            // 移除活动状态
            controlButtons.forEach(btn => btn.classList.remove('active'));
            document.querySelector(`[data-weather="${weather}"]`).classList.add('active');

            // 更新天气数据
            const data = weatherData[weather];
            temperature.textContent = data.temp;
            description.textContent = data.desc;
            city.textContent = data.city;

            // 切换背景
            document.body.style.background = data.bg;

            // 清除并创建新动画
            clearAnimations();
            if (weather === 'sunny') createSunny();
            else if (weather === 'rainy') createRain();
            else if (weather === 'snowy') createSnow();
            else if (weather === 'windy') createWind();

            // 卡片淡入效果
            weatherCard.style.opacity = '0';
            setTimeout(() => {
                weatherCard.style.opacity = '1';
            }, 100);
        }

        // 初始化
        switchWeather('sunny');

        // 事件监听
        controlButtons.forEach(button => {
            button.addEventListener('click', () => {
                switchWeather(button.dataset.weather);
            });
        });
    </script>
</body>
</html>