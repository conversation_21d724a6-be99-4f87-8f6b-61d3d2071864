<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>812所质量信息看板</title>
    <style>
        :root {
            --bg-light: #f5f7fa;
            --primary: #0066ff;
            --accent: #00c7b7;
            --font: #333;
        }

        html,
        body {
            margin: 0;
            height: 100%;
            font-family: 'Segoe UI', Arial, sans-serif;
            background: var(--bg-light);
        }

        #dashboard {
            width: 1920px;
            height: 1080px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        header {
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        header h1 {
            margin: 0;
            font-size: 32px;
            color: var(--font);
            letter-spacing: 2px;
        }

        main {
            flex: 1;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 20px;
            padding: 20px;
            box-sizing: border-box;
        }

        .module {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
            display: flex;
            flex-direction: column;
            padding: 20px;
            opacity: 0;
            transform: translateY(30px);
            animation: fadeIn 0.8s forwards;
        }

        @keyframes fadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .module h2 {
            margin: 0 0 10px;
            font-size: 24px;
            color: var(--primary);
        }

        .metrics {
            display: flex;
            flex: 1;
            align-items: center;
            justify-content: space-around;
        }

        .metric {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .metric span.value {
            font-size: 40px;
            font-weight: bold;
            color: var(--font);
        }

        .metric span.label {
            font-size: 14px;
            color: #666;
            margin-top: 6px;
        }

        .bar {
            width: 30px;
            height: 0;
            background: linear-gradient(to top, var(--accent), #80e8de);
            border-radius: 6px 6px 0 0;
            margin-top: 8px;
            transition: height 1s ease-out;
        }
    </style>
</head>

<body>
    <div id="dashboard">
        <header>
            <h1>812所质量信息看板</h1>
        </header>
        <main>
            <section class="module" id="module1">
                <h2>现场问题监控</h2>
                <div class="metrics">
                    <div class="metric"><span class="value" data-target="12">0</span>
                        <div class="bar" data-target="12"></div><span class="label">集团</span>
                    </div>
                    <div class="metric"><span class="value" data-target="8">0</span>
                        <div class="bar" data-target="8"></div><span class="label">院级</span>
                    </div>
                    <div class="metric"><span class="value" data-target="5">0</span>
                        <div class="bar" data-target="5"></div><span class="label">所级</span>
                    </div>
                    <div class="metric"><span class="value" data-target="3">0</span>
                        <div class="bar" data-target="3"></div><span class="label">其他</span>
                    </div>
                </div>
            </section>
            <section class="module" id="module2">
                <h2>测试异常追踪</h2>
                <div class="metrics">
                    <div class="metric"><span class="value" data-target="7">0</span>
                        <div class="bar" data-target="7"></div><span class="label">集团</span>
                    </div>
                    <div class="metric"><span class="value" data-target="9">0</span>
                        <div class="bar" data-target="9"></div><span class="label">院级</span>
                    </div>
                    <div class="metric"><span class="value" data-target="4">0</span>
                        <div class="bar" data-target="4"></div><span class="label">所级</span>
                    </div>
                    <div class="metric"><span class="value" data-target="2">0</span>
                        <div class="bar" data-target="2"></div><span class="label">其他</span>
                    </div>
                </div>
            </section>
            <section class="module" id="module3">
                <h2>不合格品审理分析</h2>
                <div class="metrics">
                    <div class="metric"><span class="value" data-target="6">0</span>
                        <div class="bar" data-target="6"></div><span class="label">一级审理</span>
                    </div>
                    <div class="metric"><span class="value" data-target="10">0</span>
                        <div class="bar" data-target="10"></div><span class="label">二级审理</span>
                    </div>
                    <div class="metric"><span class="value" data-target="12">0</span>
                        <div class="bar" data-target="12"></div><span class="label">三级审理</span>
                    </div>
                    <div class="metric"><span class="value" data-target="1">0</span>
                        <div class="bar" data-target="1"></div><span class="label">其他</span>
                    </div>
                </div>
            </section>
            <section class="module" id="module4">
                <h2>现场临时处理监控</h2>
                <div class="metrics">
                    <div class="metric"><span class="value" data-target="4">0</span>
                        <div class="bar" data-target="4"></div><span class="label">报警屏蔽</span>
                    </div>
                    <div class="metric"><span class="value" data-target="3">0</span>
                        <div class="bar" data-target="3"></div><span class="label">指令变更</span>
                    </div>
                    <div class="metric"><span class="value" data-target="5">0</span>
                        <div class="bar" data-target="5"></div><span class="label">软件修改</span>
                    </div>
                    <div class="metric"><span class="value" data-target="2">0</span>
                        <div class="bar" data-target="2"></div><span class="label">增减项目</span>
                    </div>
                </div>
            </section>
        </main>
    </div>
    <script>
        (function () {
            var counters = document.querySelectorAll('.value');
            var bars = document.querySelectorAll('.bar');
            var duration = 1500; // animation duration in ms
            var maxVal = 0;
            for (var i = 0; i < bars.length; i++) {
                var v = parseInt(bars[i].getAttribute('data-target'), 10);
                if (v > maxVal) { maxVal = v; }
            }

            for (var j = 0; j < counters.length; j++) {
                animateCounter(counters[j]);
            }
            for (var k = 0; k < bars.length; k++) {
                animateBar(bars[k]);
            }

            function animateCounter(el) {
                var target = parseInt(el.getAttribute('data-target'), 10) || 0;
                var startTime = null;
                function step(ts) {
                    if (!startTime) { startTime = ts; }
                    var progress = ts - startTime;
                    var percent = Math.min(progress / duration, 1);
                    el.textContent = Math.floor(percent * target);
                    if (percent < 1) { requestAnimationFrame(step); } else { el.textContent = target; }
                }
                requestAnimationFrame(step);
            }

            function animateBar(bar) {
                var val = parseInt(bar.getAttribute('data-target'), 10) || 0;
                var maxHeight = 160; // px
                var height = (val / maxVal) * maxHeight;
                setTimeout(function () {
                    bar.style.height = height + 'px';
                }, 300); // delay for effect
            }
        })();
    </script>
</body>

</html>