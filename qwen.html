<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级天气展示卡片</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #0f0f1a;
            overflow: hidden;
            position: relative;
        }

        /* 深色模式基础 */
        body {
            background: #0f0f1a;
            color: #ffffff;
        }

        /* 毛玻璃效果卡片 */
        .weather-card {
            background: rgba(30, 30, 46, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 30px;
            width: 90%;
            max-width: 400px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 10;
            transition: all 0.5s ease;
        }

        /* 天气信息样式 */
        .weather-header {
            margin-bottom: 20px;
        }

        .city-name {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .weather-description {
            font-size: 1.2rem;
            opacity: 0.8;
        }

        .temperature {
            font-size: 4rem;
            font-weight: 700;
            margin: 20px 0;
            background: linear-gradient(45deg, #00dbde, #fc00ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        /* 控制按钮样式 */
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .weather-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50px;
            padding: 10px 20px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            outline: none;
        }

        .weather-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .weather-btn.active {
            background: rgba(100, 100, 255, 0.3);
            box-shadow: 0 0 15px rgba(100, 100, 255, 0.5);
        }

        /* 天气动画容器 */
        .weather-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        /* 晴天动画 */
        .sunny-animation {
            display: none;
        }

        .sun {
            position: absolute;
            top: 10%;
            left: 10%;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, #ffeb3b, #ff9800);
            border-radius: 50%;
            box-shadow: 0 0 50px #ffeb3b;
            animation: sunMove 30s infinite linear;
        }

        .cloud {
            position: absolute;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 50px;
            filter: blur(5px);
        }

        .cloud-1 {
            top: 20%;
            left: -100px;
            width: 120px;
            height: 40px;
            animation: cloudMove 25s infinite linear;
        }

        .cloud-2 {
            top: 35%;
            left: -150px;
            width: 100px;
            height: 35px;
            animation: cloudMove 35s infinite linear;
            animation-delay: 5s;
        }

        /* 雨天动画 */
        .rainy-animation {
            display: none;
        }

        .rain {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .raindrop {
            position: absolute;
            width: 2px;
            height: 15px;
            background: linear-gradient(to bottom, rgba(255,255,255,0), rgba(174, 194, 224, 0.8));
            animation: rainFall linear infinite;
        }

        /* 雪天动画 */
        .snowy-animation {
            display: none;
        }

        .snowflake {
            position: absolute;
            top: -20px;
            color: #fff;
            font-size: 20px;
            animation: snowFall linear infinite;
        }

        /* 大风天动画 */
        .windy-animation {
            display: none;
        }

        .leaf {
            position: absolute;
            width: 20px;
            height: 20px;
            background: linear-gradient(to bottom, #8BC34A, #4CAF50);
            border-radius: 50% 0;
            animation: leafFall linear infinite;
            opacity: 0.8;
        }

        /* 动画关键帧 */
        @keyframes sunMove {
            0% { transform: translate(0, 0); }
            25% { transform: translate(100px, 20px); }
            50% { transform: translate(200px, 0); }
            75% { transform: translate(300px, -20px); }
            100% { transform: translate(400px, 0); }
        }

        @keyframes cloudMove {
            0% { transform: translateX(0); }
            100% { transform: translateX(calc(100vw + 200px)); }
        }

        @keyframes rainFall {
            0% { transform: translateY(-20px); opacity: 0; }
            10% { opacity: 1; }
            100% { transform: translateY(100vh); opacity: 0.5; }
        }

        @keyframes snowFall {
            0% { transform: translateY(-20px) translateX(0); opacity: 0; }
            10% { opacity: 1; }
            100% { transform: translateY(100vh) translateX(20px); opacity: 0.7; }
        }

        @keyframes leafFall {
            0% { transform: translateY(-20px) translateX(0) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            100% { transform: translateY(100vh) translateX(50px) rotate(360deg); opacity: 0.8; }
        }

        /* 响应式设计 */
        @media (max-width: 600px) {
            .weather-card {
                padding: 20px;
                width: 95%;
            }

            .temperature {
                font-size: 3rem;
            }

            .controls {
                gap: 10px;
            }

            .weather-btn {
                padding: 8px 15px;
                font-size: 0.8rem;
            }

            .sun {
                width: 70px;
                height: 70px;
            }
        }

        /* 平滑过渡效果 */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        .fade-out {
            animation: fadeOut 0.5s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }
    </style>
</head>
<body>
    <!-- 天气动画容器 -->
    <div class="weather-animation">
        <!-- 晴天动画 -->
        <div class="sunny-animation" id="sunnyAnimation">
            <div class="sun"></div>
            <div class="cloud cloud-1"></div>
            <div class="cloud cloud-2"></div>
        </div>

        <!-- 雨天动画 -->
        <div class="rainy-animation" id="rainyAnimation">
            <div class="rain" id="rainContainer"></div>
        </div>

        <!-- 雪天动画 -->
        <div class="snowy-animation" id="snowyAnimation">
            <div class="snow" id="snowContainer"></div>
        </div>

        <!-- 大风天动画 -->
        <div class="windy-animation" id="windyAnimation">
            <div class="wind" id="windContainer"></div>
        </div>
    </div>

    <!-- 天气卡片 -->
    <div class="weather-card">
        <div class="weather-header">
            <div class="city-name">北京</div>
            <div class="weather-description">局部多云</div>
        </div>
        <div class="temperature">25°C</div>
        <div class="controls">
            <button class="weather-btn active" data-weather="sunny">晴天</button>
            <button class="weather-btn" data-weather="rainy">雨天</button>
            <button class="weather-btn" data-weather="snowy">雪天</button>
            <button class="weather-btn" data-weather="windy">大风</button>
        </div>
    </div>

    <script>
        // 天气模式管理
        const weatherModes = {
            sunny: {
                name: "晴天",
                bgColor: "#0f0f1a",
                description: "晴朗明媚"
            },
            rainy: {
                name: "雨天",
                bgColor: "#1a1a2e",
                description: "细雨绵绵"
            },
            snowy: {
                name: "雪天",
                bgColor: "#1c1c2a",
                description: "雪花纷飞"
            },
            windy: {
                name: "大风",
                bgColor: "#1e1e2a",
                description: "风力强劲"
            }
        };

        // 当前天气模式
        let currentWeather = "sunny";

        // DOM元素
        const weatherCard = document.querySelector('.weather-card');
        const weatherButtons = document.querySelectorAll('.weather-btn');
        const weatherDescription = document.querySelector('.weather-description');
        const cityName = document.querySelector('.city-name');
        const temperature = document.querySelector('.temperature');

        // 天气动画元素
        const sunnyAnimation = document.getElementById('sunnyAnimation');
        const rainyAnimation = document.getElementById('rainyAnimation');
        const snowyAnimation = document.getElementById('snowyAnimation');
        const windyAnimation = document.getElementById('windyAnimation');
        const rainContainer = document.getElementById('rainContainer');
        const snowContainer = document.getElementById('snowContainer');
        const windContainer = document.getElementById('windContainer');

        // 切换天气模式
        function switchWeather(weather) {
            if (currentWeather === weather) return;

            // 更新按钮状态
            weatherButtons.forEach(btn => {
                if (btn.dataset.weather === weather) {
                    btn.classList.add('active');
                } else {
                    btn.classList.remove('active');
                }
            });

            // 隐藏当前动画
            hideCurrentAnimation();

            // 更新天气信息
            weatherDescription.textContent = weatherModes[weather].description;
            cityName.textContent = "北京";
            temperature.textContent = getTemperatureForWeather(weather) + "°C";

            // 显示新动画
            showAnimationForWeather(weather);

            // 更新背景色
            document.body.style.backgroundColor = weatherModes[weather].bgColor;

            // 更新当前天气
            currentWeather = weather;
        }

        // 隐藏当前动画
        function hideCurrentAnimation() {
            switch (currentWeather) {
                case "sunny":
                    sunnyAnimation.style.display = "none";
                    break;
                case "rainy":
                    rainyAnimation.style.display = "none";
                    break;
                case "snowy":
                    snowyAnimation.style.display = "none";
                    break;
                case "windy":
                    windyAnimation.style.display = "none";
                    break;
            }
        }

        // 显示指定天气的动画
        function showAnimationForWeather(weather) {
            switch (weather) {
                case "sunny":
                    sunnyAnimation.style.display = "block";
                    break;
                case "rainy":
                    rainyAnimation.style.display = "block";
                    createRaindrops();
                    break;
                case "snowy":
                    snowyAnimation.style.display = "block";
                    createSnowflakes();
                    break;
                case "windy":
                    windyAnimation.style.display = "block";
                    createLeaves();
                    break;
            }
        }

        // 为雨天创建雨滴
        function createRaindrops() {
            rainContainer.innerHTML = '';
            const raindropCount = 150;
            
            for (let i = 0; i < raindropCount; i++) {
                const raindrop = document.createElement('div');
                raindrop.className = 'raindrop';
                raindrop.style.left = Math.random() * 100 + 'vw';
                raindrop.style.animationDuration = (Math.random() * 0.5 + 0.5) + 's';
                raindrop.style.animationDelay = Math.random() * 2 + 's';
                rainContainer.appendChild(raindrop);
            }
        }

        // 为雪天创建雪花
        function createSnowflakes() {
            snowContainer.innerHTML = '';
            const snowflakeCount = 100;
            
            for (let i = 0; i < snowflakeCount; i++) {
                const snowflake = document.createElement('div');
                snowflake.className = 'snowflake';
                snowflake.innerHTML = '❄';
                snowflake.style.left = Math.random() * 100 + 'vw';
                snowflake.style.animationDuration = (Math.random() * 5 + 5) + 's';
                snowflake.style.animationDelay = Math.random() * 5 + 's';
                snowflake.style.fontSize = (Math.random() * 10 + 10) + 'px';
                snowContainer.appendChild(snowflake);
            }
        }

        // 为大风天创建叶子
        function createLeaves() {
            windContainer.innerHTML = '';
            const leafCount = 80;
            
            for (let i = 0; i < leafCount; i++) {
                const leaf = document.createElement('div');
                leaf.className = 'leaf';
                leaf.style.left = Math.random() * 100 + 'vw';
                leaf.style.animationDuration = (Math.random() * 5 + 5) + 's';
                leaf.style.animationDelay = Math.random() * 5 + 's';
                leaf.style.width = (Math.random() * 15 + 10) + 'px';
                leaf.style.height = (Math.random() * 15 + 10) + 'px';
                windContainer.appendChild(leaf);
            }
        }

        // 根据天气获取温度
        function getTemperatureForWeather(weather) {
            const temperatures = {
                sunny: 25,
                rainy: 18,
                snowy: -2,
                windy: 15
            };
            return temperatures[weather];
        }

        // 事件监听器
        weatherButtons.forEach(button => {
            button.addEventListener('click', () => {
                switchWeather(button.dataset.weather);
            });
        });

        // 初始化
        window.addEventListener('load', () => {
            showAnimationForWeather(currentWeather);
        });
    </script>
</body>
</html>