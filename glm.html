<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级天气展示卡片</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            height: 100vh;
            overflow: hidden;
            background: #1a1a1a;
            position: relative;
            transition: background 0.8s ease;
        }

        /* 天气背景 */
        .weather-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            transition: all 0.8s ease;
        }

        /* 晴天背景 */
        .sunny-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        /* 雨天背景 */
        .rainy-bg {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }

        /* 雪天背景 */
        .snowy-bg {
            background: linear-gradient(135deg, #e6e9f0 0%, #eef1f5 100%);
        }

        /* 大风背景 */
        .windy-bg {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        /* 天气动画容器 */
        .weather-animation {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        /* 太阳 */
        .sun {
            position: absolute;
            top: 10%;
            right: 15%;
            width: 120px;
            height: 120px;
            background: radial-gradient(circle, #FFD700 0%, #FFA500 70%);
            border-radius: 50%;
            box-shadow: 0 0 50px #FFD700, 0 0 100px #FFA500;
            animation: sunGlow 4s ease-in-out infinite alternate;
            opacity: 0;
            transition: opacity 0.8s ease;
        }

        .sun.active {
            opacity: 1;
        }

        @keyframes sunGlow {
            0% { box-shadow: 0 0 50px #FFD700, 0 0 100px #FFA500; }
            100% { box-shadow: 0 0 80px #FFD700, 0 0 150px #FFA500; }
        }

        /* 云朵 */
        .cloud {
            position: absolute;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 100px;
            opacity: 0;
            transition: opacity 0.8s ease;
        }

        .cloud.active {
            opacity: 1;
        }

        .cloud::before,
        .cloud::after {
            content: '';
            position: absolute;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 100px;
        }

        .cloud1 {
            width: 100px;
            height: 40px;
            top: 20%;
            left: -100px;
            animation: cloudMove 20s linear infinite;
        }

        .cloud1::before {
            width: 50px;
            height: 50px;
            top: -25px;
            left: 10px;
        }

        .cloud1::after {
            width: 60px;
            height: 40px;
            top: -15px;
            right: 10px;
        }

        .cloud2 {
            width: 80px;
            height: 35px;
            top: 40%;
            left: -80px;
            animation: cloudMove 25s linear infinite;
            animation-delay: 5s;
        }

        .cloud2::before {
            width: 40px;
            height: 40px;
            top: -20px;
            left: 15px;
        }

        .cloud2::after {
            width: 50px;
            height: 35px;
            top: -10px;
            right: 15px;
        }

        @keyframes cloudMove {
            0% { transform: translateX(0); }
            100% { transform: translateX(calc(100vw + 200px)); }
        }

        /* 雨滴 */
        .rain {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.8s ease;
        }

        .rain.active {
            opacity: 1;
        }

        .raindrop {
            position: absolute;
            width: 2px;
            height: 20px;
            background: linear-gradient(to bottom, transparent, #4FC3F7);
            animation: rainFall linear infinite;
        }

        @keyframes rainFall {
            0% {
                transform: translateY(-100vh);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh);
                opacity: 0;
            }
        }

        /* 雪花 */
        .snow {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.8s ease;
        }

        .snow.active {
            opacity: 1;
        }

        .snowflake {
            position: absolute;
            color: white;
            font-size: 1em;
            animation: snowFall linear infinite;
        }

        @keyframes snowFall {
            0% {
                transform: translateY(-100vh) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(360deg);
                opacity: 0;
            }
        }

        /* 风粒子 */
        .wind {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.8s ease;
        }

        .wind.active {
            opacity: 1;
        }

        .leaf {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #4CAF50;
            border-radius: 0 100% 0 100%;
            animation: windBlow linear infinite;
        }

        @keyframes windBlow {
            0% {
                transform: translateX(-100px) translateY(0) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateX(calc(100vw + 100px)) translateY(50px) rotate(360deg);
                opacity: 0;
            }
        }

        /* 主卡片容器 */
        .weather-card {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 90%;
            max-width: 400px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            padding: 40px 30px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .weather-card:hover {
            transform: translate(-50%, -50%) scale(1.02);
            box-shadow: 
                0 12px 40px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        /* 天气图标 */
        .weather-icon {
            font-size: 80px;
            margin-bottom: 20px;
            display: block;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
        }

        /* 城市名称 */
        .city-name {
            font-size: 24px;
            font-weight: 300;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 10px;
            letter-spacing: 1px;
        }

        /* 温度 */
        .temperature {
            font-size: 48px;
            font-weight: 200;
            color: rgba(255, 255, 255, 0.95);
            margin-bottom: 15px;
            letter-spacing: 2px;
        }

        /* 天气描述 */
        .weather-description {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 30px;
            font-weight: 300;
        }

        /* 控制按钮容器 */
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        /* 天气切换按钮 */
        .weather-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50px;
            padding: 12px 20px;
            color: rgba(255, 255, 255, 0.8);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .weather-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .weather-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .weather-btn:active::before {
            width: 300px;
            height: 300px;
        }

        .weather-btn.active {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .weather-card {
                width: 95%;
                padding: 30px 20px;
            }

            .weather-icon {
                font-size: 60px;
            }

            .temperature {
                font-size: 36px;
            }

            .city-name {
                font-size: 20px;
            }

            .weather-description {
                font-size: 16px;
            }

            .weather-btn {
                padding: 10px 16px;
                font-size: 12px;
            }

            .controls {
                gap: 10px;
            }
        }

        /* 深色模式适配 */
        body.sunny-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        body.rainy-bg {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }

        body.snowy-bg {
            background: linear-gradient(135deg, #e6e9f0 0%, #eef1f5 100%);
        }

        body.windy-bg {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        /* 雪天深色模式 */
        body.snowy-bg .weather-card,
        body.snowy-bg .weather-btn {
            background: rgba(0, 0, 0, 0.1);
            border-color: rgba(0, 0, 0, 0.2);
            color: #333;
        }

        body.snowy-bg .city-name,
        body.snowy-bg .temperature,
        body.snowy-bg .weather-description {
            color: #333;
        }

        body.snowy-bg .weather-btn {
            background: rgba(0, 0, 0, 0.1);
            border-color: rgba(0, 0, 0, 0.2);
            color: #333;
        }

        body.snowy-bg .weather-btn:hover,
        body.snowy-bg .weather-btn.active {
            background: rgba(0, 0, 0, 0.2);
            color: #000;
        }
    </style>
</head>
<body class="sunny-bg">
    <!-- 天气背景 -->
    <div class="weather-background sunny-bg"></div>

    <!-- 天气动画 -->
    <div class="weather-animation">
        <!-- 太阳 -->
        <div class="sun active"></div>
        
        <!-- 云朵 -->
        <div class="cloud cloud1 active"></div>
        <div class="cloud cloud2 active"></div>
        
        <!-- 雨滴 -->
        <div class="rain" id="rainContainer"></div>
        
        <!-- 雪花 -->
        <div class="snow" id="snowContainer"></div>
        
        <!-- 风粒子 -->
        <div class="wind" id="windContainer"></div>
    </div>

    <!-- 主卡片 -->
    <div class="weather-card">
        <div class="weather-icon" id="weatherIcon">☀️</div>
        <div class="city-name" id="cityName">北京</div>
        <div class="temperature" id="temperature">25°C</div>
        <div class="weather-description" id="weatherDescription">晴朗</div>
        
        <!-- 控制按钮 -->
        <div class="controls">
            <button class="weather-btn active" data-weather="sunny">晴天</button>
            <button class="weather-btn" data-weather="rainy">雨天</button>
            <button class="weather-btn" data-weather="snowy">雪天</button>
            <button class="weather-btn" data-weather="windy">大风</button>
        </div>
    </div>

    <script>
        // 天气数据
        const weatherData = {
            sunny: {
                icon: '☀️',
                temperature: '25°C',
                description: '晴朗',
                bgClass: 'sunny-bg'
            },
            rainy: {
                icon: '🌧️',
                temperature: '18°C',
                description: '小雨',
                bgClass: 'rainy-bg'
            },
            snowy: {
                icon: '❄️',
                temperature: '-2°C',
                description: '小雪',
                bgClass: 'snowy-bg'
            },
            windy: {
                icon: '💨',
                temperature: '22°C',
                description: '大风',
                bgClass: 'windy-bg'
            }
        };

        // 获取DOM元素
        const body = document.body;
        const weatherIcon = document.getElementById('weatherIcon');
        const temperature = document.getElementById('temperature');
        const weatherDescription = document.getElementById('weatherDescription');
        const weatherBtns = document.querySelectorAll('.weather-btn');
        const rainContainer = document.getElementById('rainContainer');
        const snowContainer = document.getElementById('snowContainer');
        const windContainer = document.getElementById('windContainer');
        const sun = document.querySelector('.sun');
        const clouds = document.querySelectorAll('.cloud');

        // 创建雨滴
        function createRaindrops() {
            rainContainer.innerHTML = '';
            for (let i = 0; i < 100; i++) {
                const raindrop = document.createElement('div');
                raindrop.className = 'raindrop';
                raindrop.style.left = Math.random() * 100 + '%';
                raindrop.style.animationDuration = Math.random() * 1 + 0.5 + 's';
                raindrop.style.animationDelay = Math.random() * 2 + 's';
                rainContainer.appendChild(raindrop);
            }
        }

        // 创建雪花
        function createSnowflakes() {
            snowContainer.innerHTML = '';
            const snowflakeSymbols = ['❄', '❅', '❆'];
            for (let i = 0; i < 50; i++) {
                const snowflake = document.createElement('div');
                snowflake.className = 'snowflake';
                snowflake.innerHTML = snowflakeSymbols[Math.floor(Math.random() * snowflakeSymbols.length)];
                snowflake.style.left = Math.random() * 100 + '%';
                snowflake.style.fontSize = Math.random() * 10 + 10 + 'px';
                snowflake.style.animationDuration = Math.random() * 3 + 2 + 's';
                snowflake.style.animationDelay = Math.random() * 2 + 's';
                snowContainer.appendChild(snowflake);
            }
        }

        // 创建风粒子（树叶）
        function createWindParticles() {
            windContainer.innerHTML = '';
            const leafColors = ['#4CAF50', '#8BC34A', '#CDDC39', '#FFC107', '#FF9800'];
            for (let i = 0; i < 30; i++) {
                const leaf = document.createElement('div');
                leaf.className = 'leaf';
                leaf.style.background = leafColors[Math.floor(Math.random() * leafColors.length)];
                leaf.style.left = Math.random() * 100 + '%';
                leaf.style.animationDuration = Math.random() * 3 + 2 + 's';
                leaf.style.animationDelay = Math.random() * 2 + 's';
                windContainer.appendChild(leaf);
            }
        }

        // 切换天气
        function changeWeather(weatherType) {
            const data = weatherData[weatherType];
            
            // 更新背景
            body.className = data.bgClass;
            
            // 更新卡片内容
            weatherIcon.innerHTML = data.icon;
            temperature.textContent = data.temperature;
            weatherDescription.textContent = data.description;
            
            // 更新按钮状态
            weatherBtns.forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.weather === weatherType) {
                    btn.classList.add('active');
                }
            });
            
            // 控制动画显示
            // 太阳和云朵（晴天）
            sun.classList.toggle('active', weatherType === 'sunny');
            clouds.forEach(cloud => cloud.classList.toggle('active', weatherType === 'sunny'));
            
            // 雨滴
            rainContainer.classList.toggle('active', weatherType === 'rainy');
            
            // 雪花
            snowContainer.classList.toggle('active', weatherType === 'snowy');
            
            // 风粒子
            windContainer.classList.toggle('active', weatherType === 'windy');
        }

        // 初始化天气动画
        createRaindrops();
        createSnowflakes();
        createWindParticles();

        // 绑定按钮事件
        weatherBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                changeWeather(btn.dataset.weather);
            });
        });

        // 添加键盘快捷键
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case '1':
                    changeWeather('sunny');
                    break;
                case '2':
                    changeWeather('rainy');
                    break;
                case '3':
                    changeWeather('snowy');
                    break;
                case '4':
                    changeWeather('windy');
                    break;
            }
        });

        // 自动切换演示（可选）
        let autoSwitch = false;
        let autoInterval;

        function toggleAutoSwitch() {
            autoSwitch = !autoSwitch;
            if (autoSwitch) {
                const weatherTypes = ['sunny', 'rainy', 'snowy', 'windy'];
                let currentIndex = 0;
                
                autoInterval = setInterval(() => {
                    changeWeather(weatherTypes[currentIndex]);
                    currentIndex = (currentIndex + 1) % weatherTypes.length;
                }, 5000);
            } else {
                clearInterval(autoInterval);
            }
        }

        // 双击卡片开启/关闭自动切换
        document.querySelector('.weather-card').addEventListener('dblclick', toggleAutoSwitch);
    </script>
</body>
</html>