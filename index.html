<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <title>812所质量信息看板</title>
        <meta name="description" content="812所质量信息看板 - 现场问题监控、测试异常追踪、不合格品审理分析、现场临时处理监控" />
        <meta name="keywords" content="质量管理,数据看板,812所" />
        <meta name="author" content="812所质量管理部门" />
        <style>
            /* CSS变量定义 - 统一颜色管理 */
            :root {
                --primary-blue: #1890ff;
                --light-blue: #e6f7ff;
                --bg-gradient-start: #f0f2f5;
                --bg-gradient-end: #e6f7ff;
                --text-primary: #262626;
                --text-secondary: #595959;
                --white: #ffffff;
                --shadow-light: rgba(0, 0, 0, 0.08);
                --shadow-medium: rgba(0, 0, 0, 0.12);
                --border-light: rgba(255, 255, 255, 0.2);
                --success-green: #52c41a;
                --warning-orange: #fa8c16;
                --error-red: #f5222d;
                --info-blue: #13c2c2;
            }

            /* 基础样式重置 */
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            html {
                font-size: 16px;
                scroll-behavior: smooth;
            }

            body {
                font-family: "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", Arial, sans-serif;
                background: linear-gradient(135deg, var(--bg-gradient-start) 0%, var(--bg-gradient-end) 100%);
                min-height: 100vh;
                overflow-x: hidden;
                color: var(--text-primary);
                line-height: 1.6;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }

            /* 主容器 */
            .dashboard-container {
                width: 100%;
                height: 100vh;
                padding: 20px;
                display: flex;
                flex-direction: column;
            }

            /* 顶部标题区域 */
            .dashboard-header {
                text-align: center;
                margin-bottom: 20px;
                padding: 20px 0;
            }

            .dashboard-title {
                font-size: 42px;
                font-weight: 700;
                color: var(--primary-blue);
                text-shadow: 0 4px 8px rgba(24, 144, 255, 0.2);
                letter-spacing: 2px;
                position: relative;
                display: inline-block;
            }

            .dashboard-title::after {
                content: "";
                position: absolute;
                bottom: -8px;
                left: 50%;
                transform: translateX(-50%);
                width: 80px;
                height: 4px;
                background: linear-gradient(90deg, var(--primary-blue), var(--info-blue));
                border-radius: 2px;
            }

            /* 主网格布局容器 */
            .dashboard-grid {
                flex: 1;
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                grid-template-rows: repeat(2, 1fr);
                gap: 24px;
                width: 100%;
                height: calc(100vh - 140px);
                max-width: 1920px;
                margin: 0 auto;
                padding: 0 20px;
            }

            /* 模块容器基础样式 */
            .module {
                background: rgba(var(--white), 0.95);
                border-radius: 16px;
                padding: 28px;
                box-shadow: 0 8px 32px var(--shadow-light);
                backdrop-filter: blur(12px);
                border: 1px solid var(--border-light);
                position: relative;
                overflow: hidden;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                min-height: 300px;
            }

            .module::before {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg, var(--primary-blue), var(--info-blue));
                border-radius: 16px 16px 0 0;
            }

            .module:hover {
                transform: translateY(-4px);
                box-shadow: 0 12px 48px var(--shadow-medium);
            }

            /* 模块标题样式 */
            .module-title {
                font-size: 20px;
                font-weight: bold;
                color: #262626;
                margin-bottom: 20px;
                text-align: center;
                border-bottom: 2px solid #1890ff;
                padding-bottom: 10px;
            }

            /* 模块内容区域 */
            .module-content {
                height: calc(100% - 60px);
                display: flex;
                flex-direction: column;
                justify-content: center;
            }

            /* 卡片网格布局 */
            .cards-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 16px;
                height: 100%;
                align-items: center;
                contain: layout style;
            }

            /* 统一卡片样式 */
            .data-card {
                background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
                border-radius: 12px;
                padding: 20px;
                text-align: center;
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
                border: 1px solid rgba(255, 255, 255, 0.3);
                position: relative;
                overflow: hidden;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                cursor: pointer;
                contain: layout style paint;
                will-change: transform, box-shadow;
            }

            .data-card::before {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 3px;
                background: var(--primary-blue);
                transform: scaleX(0);
                transition: transform 0.3s ease;
            }

            .data-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
            }

            .data-card:hover::before {
                transform: scaleX(1);
            }

            /* 卡片标题 */
            .card-title {
                font-size: 14px;
                color: var(--text-secondary);
                margin-bottom: 8px;
                font-weight: 500;
                line-height: 1.4;
            }

            /* 卡片数值 */
            .card-value {
                font-size: 28px;
                font-weight: bold;
                color: var(--primary-blue);
                margin-bottom: 4px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }

            /* 卡片单位 */
            .card-unit {
                font-size: 12px;
                color: var(--text-secondary);
                opacity: 0.8;
            }

            /* 卡片图标区域 */
            .card-icon {
                position: absolute;
                top: 12px;
                right: 12px;
                width: 24px;
                height: 24px;
                opacity: 0.3;
                transition: opacity 0.3s ease;
            }

            .data-card:hover .card-icon {
                opacity: 0.6;
            }

            /* 不同类型卡片的颜色主题 */
            .card-primary .card-value {
                color: var(--primary-blue);
            }

            .card-success .card-value {
                color: var(--success-green);
            }

            .card-warning .card-value {
                color: var(--warning-orange);
            }

            .card-error .card-value {
                color: var(--error-red);
            }

            .card-info .card-value {
                color: var(--info-blue);
            }

            /* 页面入场动画 */
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            @keyframes slideInLeft {
                from {
                    opacity: 0;
                    transform: translateX(-50px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }

            @keyframes slideInRight {
                from {
                    opacity: 0;
                    transform: translateX(50px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }

            @keyframes scaleIn {
                from {
                    opacity: 0;
                    transform: scale(0.8);
                }
                to {
                    opacity: 1;
                    transform: scale(1);
                }
            }

            /* 动画类 */
            .animate-title {
                animation: fadeInUp 0.8s ease-out;
            }

            .animate-module {
                opacity: 0;
                animation: fadeInUp 0.6s ease-out forwards;
            }

            .animate-module:nth-child(1) {
                animation-delay: 0.2s;
            }

            .animate-module:nth-child(2) {
                animation-delay: 0.4s;
            }

            .animate-module:nth-child(3) {
                animation-delay: 0.6s;
            }

            .animate-module:nth-child(4) {
                animation-delay: 0.8s;
            }

            .animate-card {
                opacity: 0;
                animation: scaleIn 0.4s ease-out forwards;
            }

            .animate-card:nth-child(1) {
                animation-delay: 0.1s;
            }

            .animate-card:nth-child(2) {
                animation-delay: 0.2s;
            }

            .animate-card:nth-child(3) {
                animation-delay: 0.3s;
            }

            .animate-card:nth-child(4) {
                animation-delay: 0.4s;
            }

            /* 交互动画效果 */
            .data-card {
                will-change: transform, box-shadow;
            }

            .card-value {
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                will-change: transform, color;
            }

            .data-card:hover .card-value {
                transform: scale(1.05);
            }

            .card-title {
                transition: color 0.3s ease;
            }

            .data-card:hover .card-title {
                color: var(--primary-blue);
            }

            /* 数值变化动画 */
            @keyframes countUp {
                from {
                    transform: translateY(20px);
                    opacity: 0;
                }
                to {
                    transform: translateY(0);
                    opacity: 1;
                }
            }

            .value-change {
                animation: countUp 0.6s ease-out;
            }

            /* 脉冲效果 */
            @keyframes pulse {
                0% {
                    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
                }
                50% {
                    box-shadow: 0 8px 24px rgba(24, 144, 255, 0.15);
                }
                100% {
                    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
                }
            }

            .pulse-effect {
                animation: pulse 2s ease-in-out infinite;
            }

            /* 浏览器兼容性前缀 */
            .data-card {
                -webkit-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                -moz-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                -o-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .card-value {
                -webkit-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                -moz-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                -o-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .data-card:hover .card-value {
                -webkit-transform: scale(1.05);
                -moz-transform: scale(1.05);
                -o-transform: scale(1.05);
            }

            /* 响应式媒体查询 */
            @media (max-width: 1440px) {
                .dashboard-grid {
                    gap: 20px;
                    padding: 0 16px;
                }

                .module {
                    padding: 24px;
                    min-height: 280px;
                }
            }

            @media (max-width: 1200px) {
                .dashboard-container {
                    padding: 16px;
                }

                .dashboard-title {
                    font-size: 28px;
                }

                .dashboard-grid {
                    gap: 16px;
                    height: calc(100vh - 120px);
                    padding: 0 12px;
                }

                .module {
                    padding: 20px;
                    min-height: 260px;
                }

                .module-title {
                    font-size: 18px;
                }
            }

            @media (max-width: 768px) {
                .dashboard-container {
                    padding: 12px;
                }

                .dashboard-grid {
                    grid-template-columns: 1fr;
                    grid-template-rows: repeat(4, auto);
                    gap: 12px;
                    height: auto;
                    padding: 0 8px;
                }

                .dashboard-title {
                    font-size: 24px;
                }

                .module {
                    padding: 16px;
                    min-height: 240px;
                }

                .module-title {
                    font-size: 16px;
                }
            }

            @media (max-width: 480px) {
                .dashboard-container {
                    padding: 8px;
                }

                .dashboard-grid {
                    gap: 8px;
                    padding: 0 4px;
                }

                .dashboard-title {
                    font-size: 20px;
                }

                .module {
                    padding: 12px;
                    min-height: 200px;
                }

                .module-title {
                    font-size: 14px;
                }
            }
        </style>
    </head>
    <body>
        <!-- 主容器 -->
        <div class="dashboard-container">
            <!-- 顶部标题区域 -->
            <div class="dashboard-header">
                <h1 class="dashboard-title animate-title">812所质量信息看板</h1>
            </div>

            <!-- 主网格布局 -->
            <div class="dashboard-grid">
                <!-- 左上模块：现场问题监控 -->
                <div class="module animate-module" id="field-issue-module">
                    <div class="module-title">现场问题监控</div>
                    <div class="module-content">
                        <!-- 模块内容将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 右上模块：测试异常追踪 -->
                <div class="module animate-module" id="test-exception-module">
                    <div class="module-title">测试异常追踪</div>
                    <div class="module-content">
                        <!-- 模块内容将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 左下模块：不合格品审理分析 -->
                <div class="module animate-module" id="quality-review-module">
                    <div class="module-title">不合格品审理分析</div>
                    <div class="module-content">
                        <!-- 模块内容将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 右下模块：现场临时处理监控 -->
                <div class="module animate-module" id="temporary-process-module">
                    <div class="module-title">现场临时处理监控</div>
                    <div class="module-content">
                        <!-- 模块内容将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- JavaScript代码 -->
        <script>
            /**
             * 812所质量信息看板
             * 静态数据结构定义
             * 符合ES5语法标准
             */
            var dashboardData = {
                // 现场问题监控数据
                fieldIssues: {
                    title: "现场问题监控",
                    data: [
                        { label: "集团级问题", value: 12, type: "error" },
                        { label: "院级问题", value: 28, type: "warning" },
                        { label: "所级问题", value: 45, type: "info" },
                        { label: "其他问题", value: 23, type: "primary" }
                    ]
                },

                // 测试异常追踪数据
                testExceptions: {
                    title: "测试异常追踪",
                    data: [
                        { label: "严重异常", value: 8, type: "error" },
                        { label: "一般异常", value: 15, type: "warning" },
                        { label: "轻微异常", value: 32, type: "info" },
                        { label: "已解决", value: 67, type: "success" }
                    ]
                },

                // 不合格品审理分析数据
                qualityReview: {
                    title: "不合格品审理分析",
                    data: [
                        { label: "一级审理", value: 18, type: "error" },
                        { label: "二级审理", value: 34, type: "warning" },
                        { label: "三级审理", value: 52, type: "info" },
                        { label: "其他审理", value: 26, type: "primary" }
                    ]
                },

                // 现场临时处理监控数据
                temporaryProcess: {
                    title: "现场临时处理监控",
                    data: [
                        { label: "紧急处理措施", value: 6, type: "error" },
                        { label: "临时解决方案", value: 14, type: "warning" },
                        { label: "预防性措施", value: 29, type: "info" },
                        { label: "标准化处理", value: 41, type: "success" }
                    ]
                }
            };

            /**
             * 数据管理器对象
             * 负责数据获取、验证和格式化
             */
            var DataManager = {
                // 获取模块数据
                getModuleData: function (moduleKey) {
                    try {
                        if (!dashboardData[moduleKey]) {
                            throw new Error("模块数据不存在: " + moduleKey);
                        }
                        return dashboardData[moduleKey];
                    } catch (error) {
                        console.error("获取模块数据失败:", error);
                        return this.getDefaultData(moduleKey);
                    }
                },

                // 计算数据百分比
                calculatePercentage: function (moduleData) {
                    try {
                        var total = 0;
                        var data = moduleData.data || [];

                        // 计算总数
                        for (var i = 0; i < data.length; i++) {
                            total += data[i].value || 0;
                        }

                        // 计算每项百分比
                        var result = [];
                        for (var j = 0; j < data.length; j++) {
                            var item = data[j];
                            var percentage = total > 0 ? Math.round((item.value / total) * 100) : 0;
                            result.push({
                                label: item.label,
                                value: item.value,
                                percentage: percentage,
                                type: item.type
                            });
                        }

                        return {
                            title: moduleData.title,
                            data: result,
                            total: total
                        };
                    } catch (error) {
                        console.error("计算百分比失败:", error);
                        return this.getDefaultCalculation();
                    }
                },

                // 数据验证
                validateData: function (data) {
                    if (!data || typeof data !== "object") {
                        return false;
                    }

                    if (!data.title || !Array.isArray(data.data)) {
                        return false;
                    }

                    for (var i = 0; i < data.data.length; i++) {
                        var item = data.data[i];
                        if (!item.label || typeof item.value !== "number" || !item.type) {
                            return false;
                        }
                    }

                    return true;
                },

                // 获取默认数据
                getDefaultData: function (moduleKey) {
                    return ErrorHandler.getFallbackData(moduleKey);
                },

                // 获取默认计算结果
                getDefaultCalculation: function () {
                    return {
                        title: "计算失败",
                        data: [{ label: "数据错误", value: 0, percentage: 0, type: "error" }],
                        total: 0
                    };
                },

                // 格式化数值显示
                formatValue: function (value) {
                    if (typeof value !== "number") {
                        return "0";
                    }
                    return value.toString();
                },

                // 格式化百分比显示
                formatPercentage: function (percentage) {
                    if (typeof percentage !== "number") {
                        return "0%";
                    }
                    return percentage + "%";
                }
            };

            /**
             * 浏览器兼容性处理管理器
             * 确保在不同浏览器环境下的正常运行
             */
            var CompatibilityManager = {
                // 检查浏览器兼容性
                init: function () {
                    this.checkGridSupport();
                    this.checkAnimationSupport();
                    this.addPolyfills();
                },

                // 检查CSS Grid支持
                checkGridSupport: function () {
                    var testElement = document.createElement("div");
                    testElement.style.display = "grid";

                    if (testElement.style.display !== "grid") {
                        console.warn("CSS Grid不支持，启用Flexbox降级方案");
                        this.enableFlexboxFallback();
                    }
                },

                // 启用Flexbox降级方案
                enableFlexboxFallback: function () {
                    var style = document.createElement("style");
                    style.textContent = ".dashboard-grid { display: flex; flex-wrap: wrap; }" + ".module { flex: 1 1 calc(50% - 12px); margin: 6px; }" + ".cards-grid { display: flex; flex-wrap: wrap; }" + ".data-card { flex: 1 1 calc(50% - 8px); margin: 4px; }";
                    document.head.appendChild(style);
                },

                // 检查动画支持
                checkAnimationSupport: function () {
                    var testElement = document.createElement("div");
                    var animationSupport = testElement.style.animationName !== undefined || testElement.style.webkitAnimationName !== undefined;

                    if (!animationSupport) {
                        console.warn("CSS动画不支持，禁用动画效果");
                        this.disableAnimations();
                    }
                },

                // 禁用动画效果
                disableAnimations: function () {
                    var style = document.createElement("style");
                    style.textContent = "* { animation: none !important; transition: none !important; }" + ".animate-title, .animate-module, .animate-card { opacity: 1 !important; }";
                    document.head.appendChild(style);
                },

                // 添加Polyfills
                addPolyfills: function () {
                    // Array.forEach polyfill for IE8
                    if (!Array.prototype.forEach) {
                        Array.prototype.forEach = function (callback, thisArg) {
                            for (var i = 0; i < this.length; i++) {
                                callback.call(thisArg, this[i], i, this);
                            }
                        };
                    }

                    // Object.keys polyfill for IE8
                    if (!Object.keys) {
                        Object.keys = function (obj) {
                            var keys = [];
                            for (var key in obj) {
                                if (obj.hasOwnProperty(key)) {
                                    keys.push(key);
                                }
                            }
                            return keys;
                        };
                    }

                    // addEventListener polyfill for IE8
                    if (!window.addEventListener) {
                        window.addEventListener = function (type, listener) {
                            window.attachEvent("on" + type, listener);
                        };
                    }
                },

                // 检查ES5支持
                checkES5Support: function () {
                    try {
                        // 测试ES5特性
                        var testArray = [1, 2, 3];
                        testArray.forEach(function () {});
                        Object.keys({});
                        return true;
                    } catch (e) {
                        console.error("ES5不支持，部分功能可能无法正常工作");
                        return false;
                    }
                }
            };

            /**
             * 错误处理系统
             * 全局错误捕获和处理机制
             */
            var ErrorHandler = {
                // 全局错误处理
                init: function () {
                    var self = this;

                    // 捕获JavaScript错误
                    window.onerror = function (message, source, lineno, colno, error) {
                        self.logError("JavaScript错误", {
                            message: message,
                            source: source,
                            line: lineno,
                            column: colno,
                            error: error
                        });
                        return true;
                    };

                    // 捕获Promise错误
                    window.addEventListener("unhandledrejection", function (event) {
                        self.logError("Promise错误", {
                            reason: event.reason
                        });
                    });
                },

                // 记录错误日志
                logError: function (type, details) {
                    var timestamp = new Date().toISOString();
                    var errorInfo = {
                        type: type,
                        timestamp: timestamp,
                        details: details,
                        userAgent: navigator.userAgent,
                        url: window.location.href
                    };

                    console.error("系统错误:", errorInfo);

                    // 可以在这里添加错误上报逻辑
                    this.reportError(errorInfo);
                },

                // 错误上报（示例）
                reportError: function (errorInfo) {
                    // 这里可以实现错误上报到服务器的逻辑
                    // 目前只在控制台输出
                    console.log("错误已记录:", errorInfo);
                },

                // 处理渲染错误
                handleRenderError: function (moduleId, error) {
                    console.error("模块渲染错误:", moduleId, error);

                    var container = document.getElementById(moduleId);
                    if (container) {
                        var contentDiv = container.querySelector(".module-content");
                        if (contentDiv) {
                            contentDiv.innerHTML = this.createErrorDisplay("数据加载失败，请刷新页面重试");
                        }
                    }
                },

                // 创建错误显示
                createErrorDisplay: function (message) {
                    return '<div style="text-align: center; color: #f5222d; padding: 20px;">' + '<div style="font-size: 24px; margin-bottom: 12px;">⚠️</div>' + '<div style="font-size: 16px; margin-bottom: 8px;">系统异常</div>' + '<div style="font-size: 14px; color: #8c8c8c;">' + message + "</div></div>";
                },

                // 数据降级处理
                getFallbackData: function (moduleKey) {
                    var fallbackData = {
                        fieldIssues: {
                            title: "现场问题监控",
                            data: [{ label: "数据加载中...", value: 0, type: "primary" }]
                        },
                        testExceptions: {
                            title: "测试异常追踪",
                            data: [{ label: "数据加载中...", value: 0, type: "primary" }]
                        },
                        qualityReview: {
                            title: "不合格品审理分析",
                            data: [{ label: "数据加载中...", value: 0, type: "primary" }]
                        },
                        temporaryProcess: {
                            title: "现场临时处理监控",
                            data: [{ label: "数据加载中...", value: 0, type: "primary" }]
                        }
                    };

                    return (
                        fallbackData[moduleKey] || {
                            title: "未知模块",
                            data: [{ label: "无数据", value: 0, type: "primary" }]
                        }
                    );
                }
            };

            /**
             * 响应式适配系统
             * 基于1920x1080设计尺寸的等比缩放
             */
            var ResponsiveManager = {
                // 设计基准尺寸
                baseWidth: 1920,
                baseHeight: 1080,

                // 初始化响应式系统
                init: function () {
                    this.handleResize();
                    this.bindEvents();
                },

                // 绑定事件
                bindEvents: function () {
                    var self = this;
                    window.addEventListener("resize", function () {
                        self.handleResize();
                    });

                    window.addEventListener("orientationchange", function () {
                        setTimeout(function () {
                            self.handleResize();
                        }, 100);
                    });
                },

                // 处理窗口尺寸变化
                handleResize: function () {
                    try {
                        var windowWidth = window.innerWidth;
                        var windowHeight = window.innerHeight;

                        // 计算缩放比例
                        var scaleX = windowWidth / this.baseWidth;
                        var scaleY = windowHeight / this.baseHeight;
                        var scale = Math.min(scaleX, scaleY, 1); // 不超过1倍缩放

                        // 应用缩放
                        this.applyScale(scale);

                        console.log("响应式缩放应用:", scale);
                    } catch (error) {
                        console.error("响应式处理失败:", error);
                    }
                },

                // 应用缩放
                applyScale: function (scale) {
                    var container = document.querySelector(".dashboard-container");
                    if (container) {
                        // 使用CSS变量控制缩放
                        container.style.setProperty("--scale-factor", scale);

                        // 动态调整字体大小
                        var baseFontSize = 16 * scale;
                        document.documentElement.style.fontSize = baseFontSize + "px";
                    }
                },

                // 获取当前缩放比例
                getCurrentScale: function () {
                    var windowWidth = window.innerWidth;
                    var windowHeight = window.innerHeight;
                    var scaleX = windowWidth / this.baseWidth;
                    var scaleY = windowHeight / this.baseHeight;
                    return Math.min(scaleX, scaleY, 1);
                }
            };

            /**
             * 模块渲染系统
             * 负责四个功能模块的DOM渲染
             */
            var ModuleRenderer = {
                // 渲染现场问题监控模块
                renderFieldIssueModule: function () {
                    try {
                        var moduleData = DataManager.getModuleData("fieldIssues");
                        var calculatedData = DataManager.calculatePercentage(moduleData);

                        if (!DataManager.validateData(moduleData)) {
                            throw new Error("现场问题监控数据验证失败");
                        }

                        var container = document.getElementById("field-issue-module");
                        var contentDiv = container.querySelector(".module-content");

                        // 创建卡片网格
                        var cardsGrid = document.createElement("div");
                        cardsGrid.className = "cards-grid";

                        // 使用文档片段批量添加卡片
                        var fragment = document.createDocumentFragment();
                        for (var i = 0; i < calculatedData.data.length; i++) {
                            var item = calculatedData.data[i];
                            var cardFragment = this.createDataCard(item, i);
                            fragment.appendChild(cardFragment);
                        }
                        cardsGrid.appendChild(fragment);

                        // 清空并添加新内容
                        contentDiv.innerHTML = "";
                        contentDiv.appendChild(cardsGrid);

                        console.log("现场问题监控模块渲染完成");
                    } catch (error) {
                        ErrorHandler.handleRenderError("field-issue-module", error);
                    }
                },

                // 创建数据卡片（优化版本）
                createDataCard: function (item, index) {
                    // 使用文档片段减少DOM操作
                    var fragment = document.createDocumentFragment();

                    var card = document.createElement("div");
                    card.className = "data-card card-" + item.type + " animate-card";
                    card.style.animationDelay = index * 0.1 + 0.5 + "s";

                    // 一次性设置innerHTML减少重排
                    card.innerHTML = '<div class="card-title">' + item.label + "</div>" + '<div class="card-value">' + DataManager.formatValue(item.value) + "</div>" + '<div class="card-unit">项 (' + DataManager.formatPercentage(item.percentage) + ")</div>";

                    fragment.appendChild(card);
                    return fragment;
                },

                // 渲染测试异常追踪模块
                renderTestExceptionModule: function () {
                    try {
                        var moduleData = DataManager.getModuleData("testExceptions");
                        var calculatedData = DataManager.calculatePercentage(moduleData);

                        if (!DataManager.validateData(moduleData)) {
                            throw new Error("测试异常追踪数据验证失败");
                        }

                        var container = document.getElementById("test-exception-module");
                        var contentDiv = container.querySelector(".module-content");

                        // 创建卡片网格
                        var cardsGrid = document.createElement("div");
                        cardsGrid.className = "cards-grid";

                        // 使用文档片段批量添加卡片
                        var fragment = document.createDocumentFragment();
                        for (var i = 0; i < calculatedData.data.length; i++) {
                            var item = calculatedData.data[i];
                            var cardFragment = this.createDataCard(item, i);
                            fragment.appendChild(cardFragment);
                        }
                        cardsGrid.appendChild(fragment);

                        // 清空并添加新内容
                        contentDiv.innerHTML = "";
                        contentDiv.appendChild(cardsGrid);

                        console.log("测试异常追踪模块渲染完成");
                    } catch (error) {
                        ErrorHandler.handleRenderError("test-exception-module", error);
                    }
                },

                // 渲染不合格品审理分析模块
                renderQualityReviewModule: function () {
                    try {
                        var moduleData = DataManager.getModuleData("qualityReview");
                        var calculatedData = DataManager.calculatePercentage(moduleData);

                        if (!DataManager.validateData(moduleData)) {
                            throw new Error("不合格品审理分析数据验证失败");
                        }

                        var container = document.getElementById("quality-review-module");
                        var contentDiv = container.querySelector(".module-content");

                        // 创建卡片网格
                        var cardsGrid = document.createElement("div");
                        cardsGrid.className = "cards-grid";

                        // 使用文档片段批量添加卡片
                        var fragment = document.createDocumentFragment();
                        for (var i = 0; i < calculatedData.data.length; i++) {
                            var item = calculatedData.data[i];
                            var cardFragment = this.createDataCard(item, i);
                            fragment.appendChild(cardFragment);
                        }
                        cardsGrid.appendChild(fragment);

                        // 清空并添加新内容
                        contentDiv.innerHTML = "";
                        contentDiv.appendChild(cardsGrid);

                        console.log("不合格品审理分析模块渲染完成");
                    } catch (error) {
                        ErrorHandler.handleRenderError("quality-review-module", error);
                    }
                },

                // 渲染现场临时处理监控模块
                renderTemporaryProcessModule: function () {
                    try {
                        var moduleData = DataManager.getModuleData("temporaryProcess");
                        var calculatedData = DataManager.calculatePercentage(moduleData);

                        if (!DataManager.validateData(moduleData)) {
                            throw new Error("现场临时处理监控数据验证失败");
                        }

                        var container = document.getElementById("temporary-process-module");
                        var contentDiv = container.querySelector(".module-content");

                        // 创建卡片网格
                        var cardsGrid = document.createElement("div");
                        cardsGrid.className = "cards-grid";

                        // 使用文档片段批量添加卡片
                        var fragment = document.createDocumentFragment();
                        for (var i = 0; i < calculatedData.data.length; i++) {
                            var item = calculatedData.data[i];
                            var cardFragment = this.createDataCard(item, i);
                            fragment.appendChild(cardFragment);
                        }
                        cardsGrid.appendChild(fragment);

                        // 清空并添加新内容
                        contentDiv.innerHTML = "";
                        contentDiv.appendChild(cardsGrid);

                        console.log("现场临时处理监控模块渲染完成");
                    } catch (error) {
                        ErrorHandler.handleRenderError("temporary-process-module", error);
                    }
                },

                // 渲染错误模块
                renderErrorModule: function (moduleId, errorMessage) {
                    var container = document.getElementById(moduleId);
                    var contentDiv = container.querySelector(".module-content");

                    contentDiv.innerHTML = '<div style="text-align: center; color: #f5222d; padding: 20px;">' + '<div style="font-size: 18px; margin-bottom: 8px;">⚠️</div>' + "<div>" + errorMessage + "</div></div>";
                }
            };

            /**
             * 功能测试验证系统
             * 确保所有功能模块正常工作
             */
            var TestValidator = {
                // 运行所有测试
                runAllTests: function () {
                    console.log("开始功能完整性测试...");

                    var results = {
                        dataStructure: this.testDataStructure(),
                        dataManager: this.testDataManager(),
                        moduleRendering: this.testModuleRendering(),
                        responsiveSystem: this.testResponsiveSystem(),
                        animationEffects: this.testAnimationEffects()
                    };

                    this.reportTestResults(results);
                    return results;
                },

                // 测试数据结构
                testDataStructure: function () {
                    try {
                        var modules = ["fieldIssues", "testExceptions", "qualityReview", "temporaryProcess"];
                        for (var i = 0; i < modules.length; i++) {
                            var moduleKey = modules[i];
                            if (!dashboardData[moduleKey] || !dashboardData[moduleKey].data) {
                                throw new Error("数据结构缺失: " + moduleKey);
                            }
                        }
                        console.log("✓ 数据结构测试通过");
                        return true;
                    } catch (error) {
                        console.error("✗ 数据结构测试失败:", error);
                        return false;
                    }
                },

                // 测试数据管理器
                testDataManager: function () {
                    try {
                        var testData = DataManager.getModuleData("fieldIssues");
                        var calculatedData = DataManager.calculatePercentage(testData);
                        var isValid = DataManager.validateData(testData);

                        if (!testData || !calculatedData || !isValid) {
                            throw new Error("数据管理器功能异常");
                        }
                        console.log("✓ 数据管理器测试通过");
                        return true;
                    } catch (error) {
                        console.error("✗ 数据管理器测试失败:", error);
                        return false;
                    }
                },

                // 测试模块渲染
                testModuleRendering: function () {
                    try {
                        var moduleIds = ["field-issue-module", "test-exception-module", "quality-review-module", "temporary-process-module"];
                        for (var i = 0; i < moduleIds.length; i++) {
                            var moduleElement = document.getElementById(moduleIds[i]);
                            if (!moduleElement) {
                                throw new Error("模块元素不存在: " + moduleIds[i]);
                            }

                            var cardsGrid = moduleElement.querySelector(".cards-grid");
                            var dataCards = moduleElement.querySelectorAll(".data-card");
                            if (!cardsGrid || dataCards.length === 0) {
                                throw new Error("模块渲染不完整: " + moduleIds[i]);
                            }
                        }
                        console.log("✓ 模块渲染测试通过");
                        return true;
                    } catch (error) {
                        console.error("✗ 模块渲染测试失败:", error);
                        return false;
                    }
                },

                // 测试响应式系统
                testResponsiveSystem: function () {
                    try {
                        var currentScale = ResponsiveManager.getCurrentScale();
                        if (typeof currentScale !== "number" || currentScale <= 0) {
                            throw new Error("响应式缩放计算异常");
                        }
                        console.log("✓ 响应式系统测试通过，当前缩放比例:", currentScale);
                        return true;
                    } catch (error) {
                        console.error("✗ 响应式系统测试失败:", error);
                        return false;
                    }
                },

                // 测试动画效果
                testAnimationEffects: function () {
                    try {
                        var animatedElements = document.querySelectorAll(".animate-title, .animate-module, .animate-card");
                        if (animatedElements.length === 0) {
                            throw new Error("动画元素未找到");
                        }
                        console.log("✓ 动画效果测试通过，找到", animatedElements.length, "个动画元素");
                        return true;
                    } catch (error) {
                        console.error("✗ 动画效果测试失败:", error);
                        return false;
                    }
                },

                // 报告测试结果
                reportTestResults: function (results) {
                    var totalTests = Object.keys(results).length;
                    var passedTests = 0;

                    for (var key in results) {
                        if (results[key]) {
                            passedTests++;
                        }
                    }

                    console.log("=== 功能完整性测试报告 ===");
                    console.log("总测试数:", totalTests);
                    console.log("通过测试:", passedTests);
                    console.log("失败测试:", totalTests - passedTests);
                    console.log("测试通过率:", Math.round((passedTests / totalTests) * 100) + "%");

                    if (passedTests === totalTests) {
                        console.log("🎉 所有功能测试通过！");
                    } else {
                        console.warn("⚠️ 部分功能测试失败，请检查相关模块");
                    }
                }
            };

            /**
             * 主程序初始化系统
             * 应用启动和事件绑定管理
             */
            var AppInitializer = {
                // 初始化应用
                init: function () {
                    try {
                        console.log("812所质量信息看板开始初始化...");

                        // 1. 初始化兼容性处理
                        CompatibilityManager.init();

                        // 2. 初始化错误处理系统
                        ErrorHandler.init();

                        // 3. 初始化响应式系统
                        ResponsiveManager.init();

                        // 4. 渲染所有模块
                        this.renderAllModules();

                        // 5. 绑定事件监听器
                        this.bindEvents();

                        // 6. 启动动画效果
                        this.triggerAnimations();

                        console.log("812所质量信息看板初始化完成");

                        // 运行功能完整性测试
                        setTimeout(function () {
                            TestValidator.runAllTests();
                        }, 1000);
                    } catch (error) {
                        ErrorHandler.logError("应用初始化失败", error);
                    }
                },

                // 渲染所有模块
                renderAllModules: function () {
                    try {
                        console.log("开始渲染所有模块...");

                        // 按顺序渲染各个模块
                        ModuleRenderer.renderFieldIssueModule();
                        ModuleRenderer.renderTestExceptionModule();
                        ModuleRenderer.renderQualityReviewModule();
                        ModuleRenderer.renderTemporaryProcessModule();

                        console.log("所有模块渲染完成");
                    } catch (error) {
                        ErrorHandler.logError("模块渲染失败", error);
                    }
                },

                // 绑定事件监听器
                bindEvents: function () {
                    try {
                        var self = this;

                        // 窗口焦点事件
                        window.addEventListener("focus", function () {
                            console.log("窗口获得焦点");
                            self.refreshData();
                        });

                        // 窗口失焦事件
                        window.addEventListener("blur", function () {
                            console.log("窗口失去焦点");
                        });

                        // 页面可见性变化
                        if (document.addEventListener) {
                            document.addEventListener("visibilitychange", function () {
                                if (!document.hidden) {
                                    console.log("页面变为可见");
                                    self.refreshData();
                                }
                            });
                        }

                        // 键盘事件（F5刷新）
                        document.addEventListener("keydown", function (event) {
                            if (event.key === "F5" || (event.ctrlKey && event.key === "r")) {
                                console.log("用户触发页面刷新");
                            }
                        });

                        console.log("事件监听器绑定完成");
                    } catch (error) {
                        ErrorHandler.logError("事件绑定失败", error);
                    }
                },

                // 触发动画效果
                triggerAnimations: function () {
                    try {
                        // 延迟触发动画，确保DOM已渲染
                        setTimeout(function () {
                            var modules = document.querySelectorAll(".module");
                            for (var i = 0; i < modules.length; i++) {
                                modules[i].classList.add("animate-module");
                            }

                            var cards = document.querySelectorAll(".data-card");
                            for (var j = 0; j < cards.length; j++) {
                                cards[j].classList.add("animate-card");
                            }

                            console.log("动画效果已触发");
                        }, 100);
                    } catch (error) {
                        ErrorHandler.logError("动画触发失败", error);
                    }
                },

                // 刷新数据
                refreshData: function () {
                    try {
                        console.log("刷新数据...");
                        // 这里可以添加数据刷新逻辑
                        // 目前使用静态数据，所以只是重新渲染
                        this.renderAllModules();
                    } catch (error) {
                        ErrorHandler.logError("数据刷新失败", error);
                    }
                }
            };

            /**
             * 应用启动入口
             * 确保DOM加载完成后初始化应用
             */
            function initializeApp() {
                AppInitializer.init();
            }

            // 页面加载完成后初始化应用
            if (document.addEventListener) {
                document.addEventListener("DOMContentLoaded", initializeApp);
            } else if (document.attachEvent) {
                // IE8兼容
                document.attachEvent("onreadystatechange", function () {
                    if (document.readyState === "complete") {
                        initializeApp();
                    }
                });
            }

            // 兼容旧版浏览器的加载事件
            if (document.readyState === "loading") {
                // 文档仍在加载中
                if (document.addEventListener) {
                    document.addEventListener("DOMContentLoaded", initializeApp);
                }
            } else {
                // 文档已加载完成
                initializeApp();
            }
        </script>
    </body>
</html>
