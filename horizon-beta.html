<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0,user-scalable=no">
  <title>812所质量信息看板</title>
  <style>
    /* 全局与背景：浅色主题（柔和蓝灰渐变 + 轻网格高光） */
    html, body {
      height: 100%;
      margin: 0;
      padding: 0;
      background:
        radial-gradient(1200px 600px at 20% -10%, rgba(0, 150, 255, 0.08), rgba(0, 150, 255, 0) 60%) no-repeat,
        radial-gradient(1200px 600px at 80% 120%, rgba(0, 150, 255, 0.06), rgba(0, 150, 255, 0) 65%) no-repeat,
        linear-gradient(180deg, #f8fbff 0%, #f3f7fc 60%, #eef3f9 100%);
      color: #1f2937; /* 深灰正文 */
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue",
                   Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Noto Sans CJK SC", sans-serif;
      overflow: auto; /* 浅色主题下允许滚动 */
    }

    /* 背景装饰：浅色细网格线条 */
    .bg-grid {
      position: fixed;
      inset: 0;
      background-image:
        linear-gradient(rgba(0,0,0,0.05) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0,0,0,0.05) 1px, transparent 1px);
      background-size: 40px 40px, 40px 40px;
      mask-image: radial-gradient(1200px 600px at 50% 40%, rgba(0,0,0,0.9), rgba(0,0,0,0.08) 70%, rgba(0,0,0,0) 100%);
      pointer-events: none;
    }

    /* 顶部标题栏 */
    .header {
      position: relative;
      height: 80px;
      padding: 0 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      letter-spacing: 1px;
      text-shadow: 0 2px 16px rgba(0, 187, 255, 0.45);
      user-select: none;
    }
    .header h1 {
      margin: 0;
      font-size: 28px;
      font-weight: 600;
      color: #0f172a; /* 深色标题 */
      line-height: 80px;
      position: relative;
    }
    .header .accent {
      color: #2563eb; /* 蓝色点缀 */
    }
    /* 标题底部动态光线 */
    .header::after {
      content: "";
      position: absolute;
      left: 10%;
      right: 10%;
      bottom: 10px;
      height: 2px;
      background: linear-gradient(90deg, rgba(37, 99, 235, 0), rgba(37, 99, 235, 0.6), rgba(37, 99, 235, 0));
      filter: blur(0.4px);
      animation: shimmer 4.8s ease-in-out infinite;
    }
    @keyframes shimmer {
      0% { transform: translateX(-5%); opacity: 0.6; }
      50% { transform: translateX(5%); opacity: 1; }
      100% { transform: translateX(-5%); opacity: 0.6; }
    }

    /* 主体区域：2x2 自适应网格 */
    .container {
      position: absolute;
      top: 80px; /* 避开标题栏 */
      bottom: 0;
      left: 0;
      right: 0;
      padding: 16px;
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-template-rows: 1fr 1fr;
      gap: 16px;
      box-sizing: border-box;
    }

    /* 模块外框样式（卡片 + 玻璃拟态 + 霓虹边） */
    .panel {
      position: relative;
      background: linear-gradient(180deg, rgba(255,255,255,0.82), rgba(255,255,255,0.94));
      border: 1px solid rgba(30, 64, 175, 0.12); /* 蓝灰浅边 */
      border-radius: 14px;
      box-shadow:
        0 2px 6px rgba(0, 0, 0, 0.04),
        0 8px 24px rgba(0, 0, 0, 0.06);
      overflow: hidden;
      transform: translateY(8px) scale(0.995);
      opacity: 0;
      animation: panel-in 700ms ease-out forwards;
    }
    /* 入场动画延迟（四宫格依次出现） */
    .panel:nth-child(1) { animation-delay: 80ms; }
    .panel:nth-child(2) { animation-delay: 180ms; }
    .panel:nth-child(3) { animation-delay: 280ms; }
    .panel:nth-child(4) { animation-delay: 380ms; }

    @keyframes panel-in {
      to {
        transform: translateY(0) scale(1);
        opacity: 1;
      }
    }

    /* 边角发光装饰 */
    .panel::before, .panel::after {
      content: "";
      position: absolute;
      pointer-events: none;
    }
    .panel::before {
      top: -30%;
      left: -10%;
      width: 50%;
      height: 80%;
      background: radial-gradient(closest-side, rgba(59, 130, 246, 0.12), rgba(59, 130, 246, 0) 70%);
      transform: rotate(8deg);
      filter: blur(6px);
    }
    .panel::after {
      bottom: -35%;
      right: -10%;
      width: 55%;
      height: 85%;
      background: radial-gradient(closest-side, rgba(99, 102, 241, 0.10), rgba(99, 102, 241, 0) 70%);
      transform: rotate(-10deg);
      filter: blur(8px);
    }

    /* 面板标题 */
    .panel-header {
      padding: 14px 16px 8px;
      display: flex;
      align-items: center;
      gap: 10px;
      border-bottom: 1px solid rgba(2, 6, 23, 0.06);
      background: linear-gradient(180deg, rgba(241, 245, 249, 0.8), rgba(241, 245, 249, 0));
    }
    .panel-header .dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      box-shadow: 0 0 10px rgba(37, 99, 235, 0.5), 0 0 20px rgba(37, 99, 235, 0.25);
      background: radial-gradient(circle at 30% 30%, #dbeafe, #60a5fa 60%, #2563eb 100%);
      flex: 0 0 auto;
    }
    .panel-title {
      font-size: 16px;
      font-weight: 600;
      color: #111827; /* 深色标题 */
      text-shadow: none;
    }

    /* 面板内容区域滚动（在内容过多时） */
    .panel-body {
      position: absolute;
      left: 0; right: 0; top: 54px; bottom: 0;
      padding: 14px;
      overflow: auto;
      scrollbar-width: thin;
      scrollbar-color: rgba(30, 58, 138, 0.25) rgba(255,255,255,0);
    }
    .panel-body::-webkit-scrollbar { width: 8px; height: 8px; }
    .panel-body::-webkit-scrollbar-thumb {
      background: linear-gradient(180deg, rgba(147,197,253,0.8), rgba(59,130,246,0.7));
      border-radius: 8px;
    }
    .panel-body::-webkit-scrollbar-track { background: transparent; }

    /* 指标卡片基础样式 */
    .cards {
      display: grid;
      grid-template-columns: repeat(2, minmax(0, 1fr)); /* 数字卡片每行两个 */
      gap: 12px;
    }
    /* 保留 two-col 类，以便未来需要时可以继续使用（与默认一致为两列） */
    .cards.two-col {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
    .card {
      position: relative;
      border-radius: 10px;
      padding: 12px 12px 10px;
      background: linear-gradient(180deg, rgba(255,255,255,0.92), rgba(255,255,255,0.98));
      border: 1px solid rgba(30, 64, 175, 0.12);
      box-shadow: 0 2px 8px rgba(0,0,0,0.04), inset 0 0 0 1px rgba(30, 64, 175, 0.05);
      transition: transform 240ms ease, box-shadow 240ms ease, border-color 240ms ease;
      overflow: hidden;
    }
    .card:hover {
      transform: translateY(-3px);
      border-color: rgba(30, 64, 175, 0.2);
      box-shadow: 0 10px 24px rgba(0, 0, 0, 0.08), inset 0 0 0 1px rgba(30, 64, 175, 0.08);
    }
    .card::after {
      content: "";
      position: absolute;
      inset: 0;
      background: radial-gradient(120px 60px at 0% 0%, rgba(147, 197, 253, 0.35), rgba(147, 197, 253, 0) 70%);
      opacity: 0.6;
      pointer-events: none;
    }
    .metric-name {
      font-size: 13px;
      color: #475569; /* 次级文字 */
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-bottom: 8px;
      letter-spacing: 0.2px;
    }
    .metric-value {
      font-size: 28px;
      font-weight: 700;
      color: #0f172a; /* 主文字深色 */
      text-shadow: none;
      line-height: 1.15;
    }
    .metric-sub {
      margin-top: 6px;
      font-size: 12px;
      color: #64748b; /* 辅助说明 */
      opacity: 0.95;
    }

    /* 卡片入场动画（瀑布式） */
    .card {
      opacity: 0;
      transform: translateY(8px);
      animation: card-in 560ms cubic-bezier(.22,.61,.36,1) forwards;
    }
    .cards .card:nth-child(1) { animation-delay: 120ms; }
    .cards .card:nth-child(2) { animation-delay: 180ms; }
    .cards .card:nth-child(3) { animation-delay: 240ms; }
    .cards .card:nth-child(4) { animation-delay: 300ms; }
    .cards .card:nth-child(5) { animation-delay: 360ms; }
    .cards .card:nth-child(6) { animation-delay: 420ms; }
    .cards .card:nth-child(7) { animation-delay: 480ms; }
    .cards .card:nth-child(8) { animation-delay: 540ms; }
    @keyframes card-in {
      to { opacity: 1; transform: translateY(0); }
    }

    /* 响应式：在较小宽度下自动堆叠 */
    @media (max-width: 1200px) {
      .container {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(4, minmax(280px, 1fr));
        overflow: auto; /* 小屏允许整体滚动 */
      }
      body { overflow: auto; }
    }

    /* 角标与发光边框动画（可选增强视觉） */
    .glow-border {
      position: absolute;
      inset: 0;
      border-radius: 14px;
      pointer-events: none;
      box-shadow:
        0 0 0 1px rgba(30, 58, 138, 0.10),
        0 0 24px rgba(59, 130, 246, 0.06) inset;
    }
  </style>
</head>
<body>
  <div class="bg-grid"></div>

  <!-- 顶部标题 -->
  <header class="header">
    <h1>
      <span class="accent">812所</span>质量信息看板
    </h1>
  </header>

  <!-- 主体 2x2 网格 -->
  <main class="container" id="gridRoot">
    <!-- 2.1 现场问题监控（左上） -->
    <section class="panel" aria-label="现场问题监控">
      <div class="glow-border"></div>
      <div class="panel-header">
        <span class="dot" aria-hidden="true"></span>
        <div class="panel-title">现场问题监控</div>
      </div>
      <div class="panel-body">
        <div class="cards">
          <div class="card">
            <div class="metric-name">集团不合格问题</div>
            <div class="metric-value" id="q1">36</div>
            <div class="metric-sub">单位：项</div>
          </div>
          <div class="card">
            <div class="metric-name">院级不合格问题</div>
            <div class="metric-value" id="q2">58</div>
            <div class="metric-sub">单位：项</div>
          </div>
          <div class="card">
            <div class="metric-name">所级不合格问题</div>
            <div class="metric-value" id="q3">112</div>
            <div class="metric-sub">单位：项</div>
          </div>
          <div class="card">
            <div class="metric-name">其他不合格问题</div>
            <div class="metric-value" id="q4">25</div>
            <div class="metric-sub">单位：项</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 2.2 测试异常追踪（右上） -->
    <section class="panel" aria-label="测试异常追踪">
      <div class="glow-border"></div>
      <div class="panel-header">
        <span class="dot" aria-hidden="true"></span>
        <div class="panel-title">测试异常追踪</div>
      </div>
      <div class="panel-body">
        <div class="cards">
          <div class="card">
            <div class="metric-name">集团测试异常</div>
            <div class="metric-value" id="t1">12</div>
            <div class="metric-sub">单位：项</div>
          </div>
          <div class="card">
            <div class="metric-name">院级测试异常</div>
            <div class="metric-value" id="t2">21</div>
            <div class="metric-sub">单位：项</div>
          </div>
          <div class="card">
            <div class="metric-name">所级测试异常</div>
            <div class="metric-value" id="t3">47</div>
            <div class="metric-sub">单位：项</div>
          </div>
          <div class="card">
            <div class="metric-name">其他测试异常</div>
            <div class="metric-value" id="t4">9</div>
            <div class="metric-sub">单位：项</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 2.3 不合格品审理分析（左下） -->
    <section class="panel" aria-label="不合格品审理分析">
      <div class="glow-border"></div>
      <div class="panel-header">
        <span class="dot" aria-hidden="true"></span>
        <div class="panel-title">不合格品审理分析</div>
      </div>
      <div class="panel-body">
        <div class="cards">
          <div class="card">
            <div class="metric-name">一级审理数量</div>
            <div class="metric-value" id="n1">31</div>
            <div class="metric-sub">单位：件</div>
          </div>
          <div class="card">
            <div class="metric-name">二级审理数量</div>
            <div class="metric-value" id="n2">18</div>
            <div class="metric-sub">单位：件</div>
          </div>
          <div class="card">
            <div class="metric-name">三级审理数量</div>
            <div class="metric-value" id="n3">7</div>
            <div class="metric-sub">单位：件</div>
          </div>
          <div class="card">
            <div class="metric-name">其他不合格品</div>
            <div class="metric-value" id="n4">15</div>
            <div class="metric-sub">单位：件</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 2.4 现场临时处理监控（右下） -->
    <section class="panel" aria-label="现场临时处理监控">
      <div class="glow-border"></div>
      <div class="panel-header">
        <span class="dot" aria-hidden="true"></span>
        <div class="panel-title">现场临时处理监控</div>
      </div>
      <div class="panel-body">
        <div class="cards two-col">
          <div class="card">
            <div class="metric-name">报警遥测波道屏蔽</div>
            <div class="metric-value" id="p1">6</div>
            <div class="metric-sub">单位：项</div>
          </div>
          <div class="card">
            <div class="metric-name">遥测遥控指令变更</div>
            <div class="metric-value" id="p2">11</div>
            <div class="metric-sub">单位：项</div>
          </div>
          <div class="card">
            <div class="metric-name">加载软件修改</div>
            <div class="metric-value" id="p3">4</div>
            <div class="metric-sub">单位：项</div>
          </div>
          <div class="card">
            <div class="metric-name">变更程序、增减项目</div>
            <div class="metric-value" id="p4">9</div>
            <div class="metric-sub">单位：项</div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <script>
    // ES5 语法：自适应尺寸微调（当窗口尺寸变化时，确保可用空间利用充分）
    (function () {
      function fitGrid() {
        var root = document.getElementById('gridRoot');
        if (!root) return;
        // 计算每个 panel 期望的最小高度，确保在非常矮的屏幕上也不至于拥挤
        var viewportH = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
        var headerH = 80;
        var availableH = viewportH - headerH - 32; // 上下 padding 合计近似
        // 最小高度保障与平衡
        var eachMin = Math.max(260, Math.floor((availableH - 16) / 2));
        root.style.gridTemplateRows = 'minmax(' + eachMin + 'px, 1fr) minmax(' + eachMin + 'px, 1fr)';
      }

      // 初次与窗口尺寸变化时执行
      fitGrid();
      if (window.addEventListener) {
        window.addEventListener('resize', fitGrid);
      } else if (window.attachEvent) {
        window.attachEvent('onresize', fitGrid);
      }

      // 数字轻微跃迁动画（装饰性，非实时刷新）
      function tweenNumber(el, to, duration) {
        if (!el) return;
        var text = el.innerHTML.replace(/[^\d.-]/g, '');
        var from = parseFloat(text);
        if (isNaN(from)) from = 0;
        to = parseFloat(to);
        if (isNaN(to)) to = 0;
        duration = duration || 700;

        var start = Date.now();
        var tick = function () {
          var now = Date.now();
          var p = Math.min(1, (now - start) / duration);
          // 缓动：easeOutCubic
          var eased = 1 - Math.pow(1 - p, 3);
          var val = Math.round(from + (to - from) * eased);
          el.innerHTML = val;
          if (p < 1) {
            if (window.requestAnimationFrame) {
              window.requestAnimationFrame(tick);
            } else {
              setTimeout(tick, 16);
            }
          }
        };
        tick();
      }

      // 初始时对所有数值做一次轻微跃迁，提升精致感
      var ids = ['q1','q2','q3','q4','t1','t2','t3','t4','n1','n2','n3','n4','p1','p2','p3','p4'];
      for (var i = 0; i < ids.length; i++) {
        var id = ids[i];
        var el = document.getElementById(id);
        if (!el) continue;
        var target = el.innerHTML;
        el.innerHTML = '0';
        tweenNumber(el, target, 800 + (i % 4) * 120);
      }
    })();
  </script>
</body>
</html>