<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>812所质量信息看板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #f0f4f8 0%, #e3eaf3 100%);
            color: #2c3e50;
            font-family: "Microsoft YaHei", sans-serif;
            min-height: 100vh;
            overflow: hidden;
        }

        .container {
            width: 100%;
            height: 100vh;
            padding: 20px;
        }

        /* 顶部标题样式 */
        .header {
            text-align: center;
            padding: 20px 0;
            margin-bottom: 30px;
            position: relative;
        }

        .header h1 {
            font-size: 36px;
            color: #1e3a8a;
            letter-spacing: 2px;
            position: relative;
            display: inline-block;
        }

        .header h1::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 120%;
            height: 4px;
            background: linear-gradient(90deg, transparent, #3b82f6, transparent);
        }

        /* 主体网格布局 */
        .grid-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            height: calc(100vh - 140px);
        }

        .grid-item {
            flex: 1 1 calc(50% - 10px);
            min-width: 300px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }

        .grid-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3b82f6, #60a5fa);
        }

        /* 模块标题样式 */
        .module-title {
            font-size: 24px;
            margin-bottom: 20px;
            color: #1e3a8a;
            position: relative;
            padding-left: 15px;
            font-weight: 600;
        }

        .module-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 20px;
            background: #3b82f6;
            border-radius: 2px;
        }

        /* 卡片网格布局 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            height: calc(100% - 60px);
        }

        .card {
            background: #f8fafc;
            border-radius: 10px;
            padding: 20px 15px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 1px solid rgba(59, 130, 246, 0.1);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(59, 130, 246, 0.1);
            border-color: rgba(59, 130, 246, 0.3);
        }

        .card-title {
            font-size: 16px;
            color: #64748b;
            margin-bottom: 12px;
            text-align: center;
            font-weight: 500;
        }

        .card-value {
            font-size: 32px;
            font-weight: bold;
            color: #3b82f6;
            line-height: 1;
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { 
                opacity: 0; 
                transform: translateY(20px); 
            }
            to { 
                opacity: 1; 
                transform: translateY(0); 
            }
        }

        .grid-item {
            animation: fadeIn 0.8s ease-out forwards;
        }

        .grid-item:nth-child(1) { animation-delay: 0.1s; }
        .grid-item:nth-child(2) { animation-delay: 0.2s; }
        .grid-item:nth-child(3) { animation-delay: 0.3s; }
        .grid-item:nth-child(4) { animation-delay: 0.4s; }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>812所质量信息看板</h1>
        </header>
        
        <div class="grid-container">
            <!-- 现场问题监控 -->
            <div class="grid-item">
                <h2 class="module-title">现场问题监控</h2>
                <div class="card-grid">
                    <div class="card">
                        <div class="card-title">集团级问题</div>
                        <div class="card-value">12</div>
                    </div>
                    <div class="card">
                        <div class="card-title">院级问题</div>
                        <div class="card-value">24</div>
                    </div>
                    <div class="card">
                        <div class="card-title">所级问题</div>
                        <div class="card-value">36</div>
                    </div>
                    <div class="card">
                        <div class="card-title">其他问题</div>
                        <div class="card-value">18</div>
                    </div>
                </div>
            </div>

            <!-- 测试异常追踪 -->
            <div class="grid-item">
                <h2 class="module-title">测试异常追踪</h2>
                <div class="card-grid">
                    <div class="card">
                        <div class="card-title">集团级异常</div>
                        <div class="card-value">8</div>
                    </div>
                    <div class="card">
                        <div class="card-title">院级异常</div>
                        <div class="card-value">16</div>
                    </div>
                    <div class="card">
                        <div class="card-title">所级异常</div>
                        <div class="card-value">28</div>
                    </div>
                    <div class="card">
                        <div class="card-title">其他异常</div>
                        <div class="card-value">14</div>
                    </div>
                </div>
            </div>

            <!-- 不合格品审理分析 -->
            <div class="grid-item">
                <h2 class="module-title">不合格品审理分析</h2>
                <div class="card-grid">
                    <div class="card">
                        <div class="card-title">一级审理</div>
                        <div class="card-value">42</div>
                    </div>
                    <div class="card">
                        <div class="card-title">二级审理</div>
                        <div class="card-value">31</div>
                    </div>
                    <div class="card">
                        <div class="card-title">三级审理</div>
                        <div class="card-value">15</div>
                    </div>
                    <div class="card">
                        <div class="card-title">其他审理</div>
                        <div class="card-value">9</div>
                    </div>
                </div>
            </div>

            <!-- 现场临时处理监控 -->
            <div class="grid-item">
                <h2 class="module-title">现场临时处理监控</h2>
                <div class="card-grid">
                    <div class="card">
                        <div class="card-title">报警遥测波道屏蔽</div>
                        <div class="card-value">23</div>
                    </div>
                    <div class="card">
                        <div class="card-title">遥测遥控指令变更</div>
                        <div class="card-value">17</div>
                    </div>
                    <div class="card">
                        <div class="card-title">加载软件修改</div>
                        <div class="card-value">11</div>
                    </div>
                    <div class="card">
                        <div class="card-title">变更程序及增减项目</div>
                        <div class="card-value">19</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 为卡片添加鼠标悬停效果
        var cards = document.getElementsByClassName('card');
        for (var i = 0; i < cards.length; i++) {
            cards[i].addEventListener('mouseover', function() {
                var value = this.querySelector('.card-value');
                value.style.transform = 'scale(1.1)';
                value.style.transition = 'transform 0.3s ease';
            });
            
            cards[i].addEventListener('mouseout', function() {
                var value = this.querySelector('.card-value');
                value.style.transform = 'scale(1)';
            });
        }

        // 添加数字增长动画
        function animateValue(element, start, end, duration) {
            var range = end - start;
            var current = start;
            var increment = end > start ? 1 : -1;
            var stepTime = Math.abs(Math.floor(duration / range));
            var timer = setInterval(function() {
                current += increment;
                element.innerHTML = current;
                if (current == end) {
                    clearInterval(timer);
                }
            }, stepTime);
        }

        // 页面加载时触发数字动画
        window.onload = function() {
            var values = document.getElementsByClassName('card-value');
            for (var i = 0; i < values.length; i++) {
                var value = parseInt(values[i].innerHTML);
                animateValue(values[i], 0, value, 1000);
            }
        };
    </script>
</body>
</html>