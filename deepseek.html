<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>高级天气展示卡片</title>
  <style>
    :root {
      --card-bg: rgba(30, 30, 46, 0.7); /* 深色毛玻璃基底 */
      --text-primary: #cdd6f4;
      --transition-duration: 0.5s;
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      min-height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: 'Segoe UI', system-ui, sans-serif;
      background: #111;
      overflow: hidden;
      position: relative;
    }
    
    /* 全屏天气背景层 */
    .weather-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
      transition: opacity var(--transition-duration) ease;
      z-index: -1;
    }
    
    /* 天气模式激活状态 */
    .weather-bg.active {
      opacity: 1;
    }
    
    /* 晴天背景 */
    #sunny-bg {
      background: linear-gradient(135deg, #1e3c72, #2a5298);
    }
    
    /* 雨天背景 */
    #rainy-bg {
      background: linear-gradient(135deg, #2c3e50, #4a6491);
    }
    
    /* 雪天背景 */
    #snowy-bg {
      background: linear-gradient(135deg, #1a2a6c, #2d388a);
    }
    
    /* 大风背景 */
    #windy-bg {
      background: linear-gradient(135deg, #232526, #414345);
    }
    
    /* 天气卡片 */
    .weather-card {
      width: 90%;
      max-width: 400px;
      background: var(--card-bg);
      backdrop-filter: blur(12px);
      -webkit-backdrop-filter: blur(12px);
      border-radius: 24px;
      padding: 30px;
      box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 2px 2px 4px rgba(255, 255, 255, 0.1),
        inset -2px -2px 4px rgba(0, 0, 0, 0.5); /* 新拟态效果 */
      z-index: 10;
    }
    
    .weather-info {
      text-align: center;
      margin-bottom: 30px;
    }
    
    .temperature {
      font-size: 4rem;
      font-weight: 700;
      color: var(--text-primary);
      margin-bottom: 10px;
    }
    
    .description {
      font-size: 1.5rem;
      color: var(--text-primary);
      opacity: 0.9;
      margin-bottom: 5px;
    }
    
    .location {
      font-size: 1.2rem;
      color: var(--text-primary);
      opacity: 0.7;
    }
    
    /* 控制按钮组 */
    .controls {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
    }
    
    .weather-btn {
      padding: 12px 0;
      border: none;
      border-radius: 12px;
      background: rgba(108, 112, 134, 0.2);
      color: var(--text-primary);
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: 
        0 4px 6px rgba(0, 0, 0, 0.1),
        inset 1px 1px 2px rgba(255, 255, 255, 0.05),
        inset -1px -1px 2px rgba(0, 0, 0, 0.3);
    }
    
    .weather-btn:hover {
      background: rgba(128, 132, 156, 0.3);
      transform: translateY(-2px);
    }
    
    .weather-btn:active {
      transform: translateY(0);
      box-shadow: 
        inset 2px 2px 4px rgba(0, 0, 0, 0.3),
        inset -2px -2px 4px rgba(255, 255, 255, 0.05);
    }
    
    /* 粒子效果容器 */
    .particles {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
    }
    
    /* 响应式调整 */
    @media (max-width: 480px) {
      .weather-card {
        padding: 20px;
      }
      
      .temperature {
        font-size: 3rem;
      }
      
      .controls {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <!-- 天气背景层 -->
  <div id="sunny-bg" class="weather-bg active"></div>
  <div id="rainy-bg" class="weather-bg"></div>
  <div id="snowy-bg" class="weather-bg"></div>
  <div id="windy-bg" class="weather-bg"></div>
  
  <!-- 粒子效果容器 -->
  <div class="particles" id="particles"></div>
  
  <!-- 天气卡片 -->
  <div class="weather-card">
    <div class="weather-info">
      <div class="temperature">25°C</div>
      <div class="description">局部多云</div>
      <div class="location">北京市</div>
    </div>
    
    <div class="controls">
      <button class="weather-btn" data-weather="sunny">晴天</button>
      <button class="weather-btn" data-weather="rainy">雨天</button>
      <button class="weather-btn" data-weather="snowy">雪天</button>
      <button class="weather-btn" data-weather="windy">大风</button>
    </div>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const weatherButtons = document.querySelectorAll('.weather-btn');
      const weatherBackgrounds = document.querySelectorAll('.weather-bg');
      const particlesContainer = document.getElementById('particles');
      
      // 当前天气状态
      let currentWeather = 'sunny';
      
      // 粒子系统
      function createParticle(type) {
        const particle = document.createElement('div');
        particle.style.position = 'absolute';
        particle.style.pointerEvents = 'none';
        
        switch(type) {
          case 'rain':
            particle.style.width = '2px';
            particle.style.height = '15px';
            particle.style.background = 'rgba(174, 194, 224, 0.7)';
            particle.style.top = '-20px';
            particle.style.left = `${Math.random() * 100}%`;
            particle.style.animation = `fall linear ${(Math.random() * 1) + 0.5}s`;
            break;
            
          case 'snow':
            particle.style.width = '8px';
            particle.style.height = '8px';
            particle.style.background = 'rgba(255, 255, 255, 0.8)';
            particle.style.borderRadius = '50%';
            particle.style.top = '-10px';
            particle.style.left = `${Math.random() * 100}%`;
            particle.style.animation = `snowFall linear ${(Math.random() * 5) + 5}s`;
            break;
            
          case 'wind':
            particle.style.width = `${Math.random() * 20 + 5}px`;
            particle.style.height = '2px';
            particle.style.background = 'rgba(200, 200, 200, 0.6)';
            particle.style.top = `${Math.random() * 100}%`;
            particle.style.left = '-50px';
            particle.style.animation = `windBlow linear ${(Math.random() * 3) + 2}s`;
            break;
        }
        
        particlesContainer.appendChild(particle);
        
        // 移除粒子当动画结束
        particle.addEventListener('animationend', () => {
          particle.remove();
        });
      }
      
      // 粒子动画定义
      function defineParticleAnimations() {
        const style = document.createElement('style');
        style.textContent = `
          @keyframes fall {
            to { transform: translateY(calc(100vh + 20px)); }
          }
          
          @keyframes snowFall {
            to { transform: translateY(calc(100vh + 10px)) translateX(${Math.random() > 0.5 ? '-' : ''}${Math.random() * 100}px); }
          }
          
          @keyframes windBlow {
            to { transform: translateX(calc(100vw + 50px)); }
          }
        `;
        document.head.appendChild(style);
      }
      
      // 初始化粒子动画
      defineParticleAnimations();
      
      // 切换天气函数
      function switchWeather(weatherType) {
        if (weatherType === currentWeather) return;
        
        // 更新背景
        weatherBackgrounds.forEach(bg => {
          bg.classList.remove('active');
          if (bg.id === `${weatherType}-bg`) {
            setTimeout(() => bg.classList.add('active'), 10);
          }
        });
        
        // 清除旧粒子
        particlesContainer.innerHTML = '';
        
        // 根据天气生成粒子
        if (weatherType === 'rainy') {
          setInterval(() => {
            for (let i = 0; i < 5; i++) createParticle('rain');
          }, 100);
        } 
        else if (weatherType === 'snowy') {
          setInterval(() => {
            for (let i = 0; i < 3; i++) createParticle('snow');
          }, 300);
        } 
        else if (weatherType === 'windy') {
          setInterval(() => {
            for (let i = 0; i < 4; i++) createParticle('wind');
          }, 200);
        }
        
        // 更新当前天气
        currentWeather = weatherType;
        
        // 更新卡片信息（示例数据）
        const tempElem = document.querySelector('.temperature');
        const descElem = document.querySelector('.description');
        
        switch(weatherType) {
          case 'sunny':
            tempElem.textContent = '25°C';
            descElem.textContent = '晴朗';
            break;
          case 'rainy':
            tempElem.textContent = '18°C';
            descElem.textContent = '中雨';
            break;
          case 'snowy':
            tempElem.textContent = '-2°C';
            descElem.textContent = '大雪';
            break;
          case 'windy':
            tempElem.textContent = '15°C';
            descElem.textContent = '大风';
            break;
        }
      }
      
      // 绑定按钮事件
      weatherButtons.forEach(btn => {
        btn.addEventListener('click', () => {
          switchWeather(btn.dataset.weather);
        });
      });
    });
  </script>
</body>
</html>